# Amazon API 400 Error Fixes

## Problem Analysis

The 400 Bad Request error was occurring when fetching Amazon orders due to several parameter format issues:

### Error Details
```
[Nest] 13870  - 08/06/2025, 6:00:22 PM   ERROR [AmazonService] 400 | Request failed with status code 400
[Nest] 13870  - 08/06/2025, 6:00:22 PM   ERROR [AmazonService] Error fetching orders: Request failed with status code 400
```

### Request Parameters That Were Failing
```json
{
  "MarketplaceIds": "ATVPDKIKX0DER",  // ❌ Should be array
  "LastUpdatedAfter": "2025-06-07T00:57:59.035Z",
  "LastUpdatedBefore": "2025-06-14T00:57:59.035Z",
  "MaxResultsPerPage": 100,
  "OrderStatuses": [
    "Unshipped",
    "PartiallyShipped", 
    "Shipped",
    "Canceled",
    "Unfulfillable"
  ]
}
```

## Root Causes Identified

### 1. **MarketplaceIds Format Issue** ⚠️
- **Problem**: `MarketplaceIds` was being sent as a string
- **Amazon Requirement**: Must be an array of strings
- **Fix**: Changed from `"ATVPDKIKX0DER"` to `["ATVPDKIKX0DER"]`

### 2. **Date Range Validation Issues**
- **Problem**: Dates could be in the future or improperly formatted
- **Fix**: Added comprehensive date validation and timezone handling

### 3. **OrderStatuses Parameter**
- **Problem**: May have been causing validation issues
- **Fix**: Removed to use Amazon's default (all statuses)

### 4. **Parameter Validation Missing**
- **Problem**: No validation before sending requests to Amazon
- **Fix**: Added comprehensive parameter validation

## Fixes Implemented

### 1. Fixed MarketplaceIds Format
```typescript
// Before (❌ Incorrect)
const params = {
  MarketplaceIds: this.MarketplaceIds, // String
  // ...
};

// After (✅ Correct)
const params = {
  MarketplaceIds: [this.MarketplaceIds], // Array
  // ...
};
```

### 2. Enhanced Date Handling
```typescript
// Proper date formatting with timezone handling
const endDate = new Date();
endDate.setHours(23, 59, 59, 999); // End of today

const startDate = new Date();
startDate.setMonth(startDate.getMonth() - 2);
startDate.setHours(0, 0, 0, 0); // Start of day
```

### 3. Added Parameter Validation
```typescript
private validateOrderParams(params: any): void {
  if (!params.MarketplaceIds) {
    throw new Error('MarketplaceIds is required');
  }
  
  if (!Array.isArray(params.MarketplaceIds)) {
    throw new Error('MarketplaceIds must be an array');
  }
  
  if (params.MarketplaceIds.length === 0) {
    throw new Error('MarketplaceIds array cannot be empty');
  }
  
  // Date validation
  if (params.LastUpdatedAfter) {
    const date = new Date(params.LastUpdatedAfter);
    if (isNaN(date.getTime())) {
      throw new Error('Invalid LastUpdatedAfter date format');
    }
    if (date > new Date()) {
      throw new Error('LastUpdatedAfter cannot be in the future');
    }
  }
  
  // Additional validations...
}
```

### 4. Improved Error Handling
```typescript
} else if (response?.status === 400) {
  this.logger.error('400 Bad Request - Invalid parameters');
  this.logger.error('Response data:', JSON.stringify(response?.data, null, 2));
  // Don't retry 400 errors as they indicate invalid parameters
  return Promise.reject(error);
}
```

### 5. Enhanced Date Chunking
```typescript
private createDateChunks(startDate: Date, endDate: Date, chunkDays: number) {
  const chunks: Array<{ start: Date; end: Date }> = [];
  const current = new Date(startDate);
  const now = new Date();

  // Ensure we don't go beyond current time
  const maxEndDate = endDate > now ? now : endDate;

  while (current < maxEndDate) {
    const chunkEnd = new Date(current);
    chunkEnd.setDate(chunkEnd.getDate() + chunkDays);
    chunkEnd.setHours(23, 59, 59, 999); // End of day

    if (chunkEnd > maxEndDate) {
      chunkEnd.setTime(maxEndDate.getTime());
    }

    // Only add chunk if start is before end
    if (current < chunkEnd) {
      chunks.push({
        start: new Date(current),
        end: new Date(chunkEnd),
      });
    }

    current.setDate(current.getDate() + chunkDays);
    current.setHours(0, 0, 0, 0); // Start of next day
  }

  return chunks;
}
```

## Files Modified

1. **src/modules/product/services/amazon.service.ts**
   - Fixed MarketplaceIds format in all API calls
   - Added parameter validation
   - Enhanced error handling for 400 errors
   - Improved date handling and chunking
   - Added comprehensive logging

## Testing the Fix

### 1. Verify Parameter Format
```bash
# Check logs for proper parameter format
docker logs <container> | grep "Request params"
```

### 2. Monitor for 400 Errors
```bash
# Should no longer see 400 errors
docker logs <container> | grep "400"
```

### 3. Test API Endpoints
```bash
# Test the optimized order fetching
POST /api/product/amazon/orders/last-month
```

## Expected Results

### Before Fix
- ❌ 400 Bad Request errors
- ❌ Failed order fetching
- ❌ Invalid parameter format

### After Fix
- ✅ Successful API calls
- ✅ Proper parameter validation
- ✅ Orders fetched successfully
- ✅ Comprehensive error handling

## Amazon API Requirements Compliance

### MarketplaceIds Parameter
- ✅ **Format**: Array of strings
- ✅ **Required**: Yes
- ✅ **Validation**: Non-empty array

### Date Parameters
- ✅ **Format**: ISO 8601 (YYYY-MM-DDTHH:mm:ssZ)
- ✅ **Validation**: Not in future
- ✅ **Timezone**: UTC

### MaxResultsPerPage
- ✅ **Range**: 1-100
- ✅ **Default**: 100 (maximum for efficiency)

## Monitoring and Maintenance

### 1. Log Monitoring
- Watch for parameter validation errors
- Monitor API response codes
- Track rate limiting compliance

### 2. Error Handling
- 400 errors are no longer retried (correct behavior)
- Comprehensive error logging for debugging
- Proper error propagation to queue system

### 3. Performance
- Optimized parameter format reduces API errors
- Better error handling prevents unnecessary retries
- Improved logging for troubleshooting

## Conclusion

The 400 error was primarily caused by incorrect parameter formatting, specifically the MarketplaceIds being sent as a string instead of an array. The comprehensive fixes ensure:

1. **Compliance** with Amazon SP-API requirements
2. **Robust validation** before API calls
3. **Better error handling** and logging
4. **Improved reliability** of order fetching

The system should now successfully fetch Amazon orders without 400 errors.
