import { ILike, Repository } from 'typeorm';
import { ColorNameEntity } from '../../entities/color-name.entity';
import { ColorEntity } from '../../entities/color.entity';

export async function getColor(
  colorRepository: Repository<ColorEntity>,
  colorNameRepository: Repository<ColorNameEntity>,
  sku: string,
): Promise<string | null> {
  const parts = sku?.split('-');

  if (sku.includes('Blue-Red-Black')) {
    return 'Blue-Red-Black';
  }

  if (sku.includes('Black-White-Blue')) {
    return 'Black White Blue';
  }

  for (const part of parts) {
    const colorEntity = await colorRepository.findOne({
      where: [{ color: ILike(part) }],
    });

    if (colorEntity) {
      return colorEntity.color;
    }

    const colorNameEntity = await colorNameRepository.findOne({
      where: [{ value: ILike(part) }],
      relations: ['color'],
    });

    if (colorNameEntity) {
      return colorNameEntity.color?.color ?? '';
    }
  }

  return '';
}
