import { SkuParts } from '../../common/interfaces/SkuParts';
import { ProductService } from '../product/services/product.service';
import { getRingStyle } from './ring-style.util';

export async function parseShopifyOrders(
  item: any,
  productService: ProductService,
): Promise<SkuParts> {
  // Extracting properties from the item
  const { sku, quantity, price, properties, title } = item;

  // Extracting specific properties from the item's properties array
  const insideEngraving =
    properties.find((p) => p.name.includes('Inside Engraving'))?.value ?? '';
  const goldInlay =
    properties.find((property) => property.name === 'Bake in Gold Inlay?')
      ?.value ?? '';
  let outsideEngraving =
    properties.find((property) => property.name.includes('Outside Engraving'))
      ?.value ?? '';

  // Parsing size
  let size = 0;
  try {
    const sizeStr =
      properties.find((property) => property.name === 'Size')?.value ?? '0';
    const sizeMatch = sizeStr.match(/^(\d+)/);
    size = sizeMatch ? parseInt(sizeMatch[1]) : 0;
  } catch (e) {
    size = 0;
  }

  // Parsing color
  let color = properties.find(
    (property) =>
      property.name.toLowerCase().includes('color') ||
      property.name.toLowerCase().includes('colour'),
  )?.value;

  // Get ring style
  let style = getRingStyle(sku);

  // Fetching product info from DataService
  const product = await productService.findProduct({ sku });

  // Setting default values if necessary
  if (!outsideEngraving) outsideEngraving = product?.productInfo?.design ?? '';
  if (!size) size = product?.productInfo?.size ?? 0;
  if (product?.productInfo?.color) color = product.productInfo.color;

  if (size == 0) {
    outsideEngraving = '';
  }
  // Setting style if not already set
  if (!style && title.toLocaleLowerCase().includes('dual layer')) {
    style = 'D6';
  }
  // Setting style if not already set
  if (!style && title.toLocaleLowerCase().includes('4mm or 6mm')) {
    if (
      properties
        .find((property) => property.name === 'Size')
        ?.value?.includes('4mm')
    ) {
      style = 'CF4';
    }
    if (
      properties
        .find((property) => property.name === 'Size')
        ?.value?.includes('6mm')
    ) {
      style = 'CF4';
    }
  }

  // Returning the parsed data
  return {
    style: style ?? '',
    color: color ?? '',
    size,
    price: parseFloat(price),
    insideEngraving,
    outsideEngraving,
    goldInlay,
    quantity: parseInt(quantity),
    sku,
  };
}
