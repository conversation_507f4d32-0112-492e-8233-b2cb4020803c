export const convertToSQLDateTime = (
  isoDate,
  isEndOfDay = false,
  isDevelopment = true,
) => {
  if (isDevelopment) {
    const dateObject = new Date(isoDate);
    const year = dateObject.getFullYear();
    const month = String(dateObject.getMonth() + 1).padStart(2, '0');
    const day = String(dateObject.getDate()).padStart(2, '0');
    const time = isEndOfDay ? '23:59:59' : '00:00:00';
    return `${year}-${month}-${day} ${time}`;
  } else {
    const dateObject = convertToSQLDateTimeWithoutTime(isoDate);
    const year = dateObject.getFullYear();
    const month = String(dateObject.getMonth() + 1).padStart(2, '0');
    const day = String(dateObject.getDate()).padStart(2, '0');
    const time = isEndOfDay ? '23:59:59' : '00:00:00';
    return `${year}-${month}-${day} ${time}`;
  }
};

export const convertToSQLDateTimeWithoutTime = (isoDate) => {
  return new Date(new Date(isoDate).getTime() - 7 * 60 * 60 * 1000);
};

export function formatDate(date: Date): string {
  const monthNames = [
    'Jan',
    'Feb',
    'Mar',
    'Apr',
    'May',
    'Jun',
    'Jul',
    'Aug',
    'Sep',
    'Oct',
    'Nov',
    'Dec',
  ];
  const month = monthNames[date.getMonth()];
  const day = date.getDate();
  return `${month} ${day}`;
}
