export function getRingStyle(sku: string): string | null {
  const matches = {
    B4: sku.includes('BevelCF') && sku.includes('4mm'),
    B6: sku.includes('BevelCF') && sku.includes('6mm'),
    B8:
      (sku.includes('BevelCF') && sku.includes('8mm')) ||
      sku.includes('Bevel8mm') ||
      sku.includes('BevelCF-8mm'),
    BevelCF: sku.includes('BevelCF'),
    Stripe:
      sku.includes('Bevel') ||
      sku.includes('Stripe') ||
      sku.includes('Striped'),
    D6: sku.includes('D6'),
    SCF: sku.includes('SCF'),
    Pyra: sku.includes('Pyra'),
    StackableTrio: sku.includes('StackableTrio'),
    Braid: sku.includes('Braid'),
    Checkerplate: sku.includes('Checkerplate'),
    KnifeRing: sku.includes('Knife'),
    DM: sku.includes('DM'),
    CF6:
      sku.includes('ComfortFit-6mm') ||
      sku.includes('CF-6mm') ||
      sku.includes('CF6') ||
      sku.includes('C6'),
    DW: sku.includes('DW'),
    CF4:
      sku.includes('ComfortFit-4mm') ||
      sku.includes('CF-4mm') ||
      sku.includes('CF4') ||
      sku.includes('C4'),
    CF2:
      sku.includes('ComfortFit-2mm') ||
      sku.includes('CF-2mm') ||
      sku.includes('CF2') ||
      sku.includes('C2'),
    DF6:
      sku.includes('DF6') ||
      sku.includes('CF-2mm') ||
      sku.includes('CF2') ||
      sku.includes('C2'),
  };

  for (const [style, match] of Object.entries(matches)) {
    if (match) {
      return style;
    }
  }

  return null;
}
