export function extractNumberFromSku(sku: string): number {
  const predefinedSkus: { [key: string]: number } = {
    'ComfortFit-6mm-Black-R11': 11,
    'ComfortFit-6mm-Black-11': 12,
    'ComfortFit-6mm-Black-12': 13,
    'ComfortFit-6mm-Black-13': 14,
    'ComfortFit-6mm-Black-14': 15,
    'ComfortFit-6mm-Black-15': 16,
    'ComfortFit-6mm-DarkSilver-R11': 11,
    'ComfortFit-6mm-DarkSilver-11': 12,
    'ComfortFit-6mm-DarkSilver-12': 13,
    'ComfortFit-6mm-DarkSilver-13': 14,
    'ComfortFit-6mm-DarkSilver-14': 15,
    'ComfortFit-6mm-DarkSilver-15': 16,
    'ComfortFit-6mm-Silver-R11': 11,
    'ComfortFit-6mm-Silver-11': 12,
    'ComfortFit-6mm-Silver-12': 13,
    'ComfortFit-6mm-Silver-13': 14,
    'ComfortFit-6mm-Silver-14': 15,
    'ComfortFit-6mm-Silver-15': 16,
  };

  // Check for predefined SKUs
  if (predefinedSkus[sku]) {
    return predefinedSkus[sku];
  }

  // Extract numbers using regular expressions
  const skuRNumberMatch = sku.match(/R(\d+)/);
  const skuSNumberMatch = sku.match(/S(\d+)/);
  const skuNumberMatch = sku.match(/\b(\d+)\b/);

  if (skuRNumberMatch) {
    return parseInt(skuRNumberMatch[1], 10);
  }

  if (skuSNumberMatch) {
    return parseInt(skuSNumberMatch[1], 10);
  }

  if (skuNumberMatch) {
    return parseInt(skuNumberMatch[1], 10);
  }

  // Check each part of the SKU
  const parts = sku.split('-');
  for (const part of parts) {
    if (part.includes('mm')) {
      continue;
    }

    const numberMatch = part.match(/\d+/);
    if (numberMatch) {
      return parseInt(numberMatch[0], 10);
    }
  }

  return 0;
}
