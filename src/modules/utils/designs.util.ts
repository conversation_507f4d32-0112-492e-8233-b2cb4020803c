import { ILike, Repository } from 'typeorm';
import { DesignNameEntity } from '../../entities/design-name.entity';
import { DesignEntity } from '../../entities/design.entity';

export async function getDesignFromSku(
  designRepository: Repository<DesignEntity>,
  designNameRepository: Repository<DesignNameEntity>,
  sku: string,
): Promise<string | null> {
  const parts = sku?.split('-');

  for (const part of parts) {
    const designEntity = await designRepository
      .createQueryBuilder('designEntity')
      .where('designEntity.design ilike :part', { part: part })
      .getOne();

    if (designEntity) {
      return designEntity.design;
    }

    const designNameEntity = await designNameRepository.findOne({
      where: [{ value: ILike(part) }],
      relations: ['design'],
    });

    if (designNameEntity) {
      return designNameEntity.design?.design ?? '';
    }
  }

  return '';
}

export async function createDesign(
  designRepository: Repository<DesignEntity>,
  design: string,
) {
  let designEntity = await designRepository.findOne({ where: { design } });

  if (!designEntity) {
    designEntity = designRepository.create({ design });
    await designRepository.save(designEntity);
  }

  return designEntity;
}
