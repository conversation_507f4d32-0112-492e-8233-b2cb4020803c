import { google } from 'googleapis';
import { Injectable, Logger } from '@nestjs/common';
import path from 'path';
@Injectable()
export class GoogleDriveService {
  private readonly logger = new Logger(GoogleDriveService.name);
  private drive;

  constructor() {
    const keyFilePath = path.join(
      __dirname,
      'knot-app-425607-993d33dbe38b.json',
    );

    const auth = new google.auth.GoogleAuth({
      keyFile: keyFilePath,
      scopes: ['https://www.googleapis.com/auth/drive'],
    });

    this.drive = google.drive({ version: 'v3', auth });
  }

  async loadSvgFile(): Promise<string[] | null> {
    try {
      const folderId = process.env.GOOGLE_DRIVE_FOLDER_ID;
      let files: string[] = [];
      let pageToken: string | null = null;

      do {
        const response = await this.drive.files.list({
          q: `'${folderId}' in parents`,
          fields: 'nextPageToken, files(id, name)',
          pageToken: pageToken || undefined,
        });

        if (response.data.files) {
          files = files.concat(response.data.files.map((file) => file.name));
        }

        pageToken = response.data.nextPageToken;
      } while (pageToken);

      this.logger.debug(files);

      return files;
    } catch (error) {
      this.logger.error(`Failed to load SVG file from Google Drive: ${error}`);
      return null;
    }
  }

  // async loadSvgFile(fileName: string): Promise<string | null> {
  //   try {
  //     const folderId = process.env.GOOGLE_DRIVE_FOLDER_ID;
  //     this.logger.debug(fileName);

  //     const response = await this.drive.files.list({
  //       q: `'${folderId}' in parents and name='${fileName}.svg'`,
  //       fields: 'files(id, name)',
  //     });

  //     this.logger.debug(response.data);
  //     const files = response.data.files;

  //     if (files.length === 0) {
  //       this.logger.warn(
  //         `No SVG file named "${fileName}" found in folder ID "${folderId}".`,
  //       );
  //       return null;
  //     }

  //     return 'true';

  //     this.logger.verbose(response.data.files);
  //     const fileId = files[0].id;

  //     const fileResponse = await this.drive.files.get(
  //       { fileId, alt: 'media' },
  //       { responseType: 'stream' },
  //     );

  //     return new Promise((resolve, reject) => {
  //       let fileContent = '';
  //       fileResponse.data
  //         .on('data', (chunk) => {
  //           fileContent += chunk;
  //         })
  //         .on('end', () => {
  //           this.logger.log(`Successfully downloaded SVG file: ${fileName}`);
  //           resolve(fileContent);
  //         })
  //         .on('error', (err) => {
  //           this.logger.error(`Error downloading SVG file: ${err}`);
  //           reject(null);
  //         });
  //     });
  //   } catch (error) {
  //     this.logger.error(`Failed to load SVG file from Google Drive: ${error}`);
  //     return null;
  //   }
  // }
}
