import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { ConfigController } from './config.controller';
import { ConfigService } from './config.service';
import { ColorNameEntity } from '../../entities/color-name.entity';
import { ColorEntity } from '../../entities/color.entity';
import { DesignEntity } from '../../entities/design.entity';
import { DesignNameEntity } from '../../entities/design-name.entity';
import { ConfigEntity } from '../../entities/config.entity';
import { GoogleDriveService } from '../utils/google-drive.service';

@Module({
  imports: [
    TypeOrmModule.forFeature([
      ConfigEntity,
      ColorEntity,
      ColorNameEntity,
      DesignEntity,
      DesignNameEntity,
    ]),
  ],
  controllers: [ConfigController],
  providers: [ConfigService, GoogleDriveService],
})
export class SettingModule {}
