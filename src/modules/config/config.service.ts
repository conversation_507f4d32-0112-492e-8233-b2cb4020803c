import { Injectable, Logger, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { ColorEntity } from '../../entities/color.entity';
import { DesignEntity } from '../../entities/design.entity';
import { DesignNameEntity } from '../../entities/design-name.entity';
import { ColorNameEntity } from '../../entities/color-name.entity';
import { ConfigEntity } from '../../entities/config.entity';

@Injectable()
export class ConfigService {
  private readonly logger = new Logger(ConfigService.name);

  constructor(
    @InjectRepository(ColorEntity)
    private readonly colorRepository: Repository<ColorEntity>,
    @InjectRepository(ConfigEntity)
    private readonly configRepository: Repository<ConfigEntity>,
    @InjectRepository(DesignEntity)
    private readonly designRepository: Repository<DesignEntity>,
    @InjectRepository(DesignNameEntity)
    private readonly designNameRepository: Repository<DesignNameEntity>,
    @InjectRepository(ColorNameEntity)
    private readonly colorNameRepository: Repository<ColorNameEntity>,
  ) {}

  async setConfig(
    key: string,
    value: string,
    description?: string,
  ): Promise<void> {
    let config = await this.configRepository.findOne({ where: { key } });
    if (config) {
      config.value = value;
      if (description) config.description = description;
    } else {
      config = this.configRepository.create({ key, value, description });
    }
    await this.configRepository.save(config);
  }

  async getConfig(key: string): Promise<string | null> {
    const config = await this.configRepository.findOne({ where: { key } });
    return config ? config.value : null;
  }

  async getConfigWithDescription(key: string): Promise<ConfigEntity | null> {
    return await this.configRepository.findOne({ where: { key } });
  }

  async getAllColors(): Promise<ColorEntity[]> {
    return this.colorRepository.find({
      relations: ['names'],
      order: { color: 'ASC' },
    });
  }

  async getAllDesign(): Promise<DesignEntity[]> {
    return this.designRepository.find({
      relations: ['names'],
      order: { design: 'ASC' },
    });
  }

  async addColor(colorDto: any): Promise<ColorEntity> {
    const tmp = this.colorRepository.create({
      color: colorDto.color.trim(),
      code: colorDto.code,
    });
    const newColor = await this.colorRepository.save(tmp);
    if (colorDto.names && colorDto.names.length > 0) {
      for (const nameValue of colorDto.names) {
        if (nameValue.length > 0) {
          const newName = this.colorNameRepository.create({
            value: nameValue.trim(),
            color: newColor,
          });
          await this.colorNameRepository.save(newName);
          colorDto.names?.push(newName);
        }
      }
    }

    try {
      const item = await this.colorRepository.save(newColor);
      // this.dataService.updateColors();
      return item;
    } catch (error) {
      this.logger.error(error);
      throw new NotFoundException(`Failed to add the new color`);
    }
  }

  async editColor(
    id: number,
    color: string,
    code: string,
    names?: string[],
  ): Promise<ColorEntity> {
    try {
      const existingColor = await this.colorRepository.findOneOrFail({
        where: {
          id: id,
        },
        relations: ['names'],
      });

      if (names) {
        await this.colorNameRepository.delete({
          color: existingColor,
        });
        existingColor.names = [];

        for (const nameValue of names) {
          if (nameValue.length > 0) {
            const newName = this.colorNameRepository.create({
              value: nameValue.trim(),
              color: existingColor,
            });

            await this.colorNameRepository.save(newName);
            existingColor.names?.push(newName);
          }
        }
      }

      existingColor.color = color.trim();
      existingColor.code = code;

      const updatedColor = await this.colorRepository.save(existingColor);
      // this.dataService.updateColors();

      return updatedColor;
    } catch (error) {
      this.logger.error(error);
      this.logger.error(JSON.stringify(error));
      throw new NotFoundException(`Design with ID ${id} not found`);
    }
  }

  async addDesign(designDto: any): Promise<DesignEntity> {
    try {
      const tmp = this.designRepository.create({
        design: designDto.design.trim(),
      });

      const newDesign = await this.designRepository.save(tmp);
      if (designDto.names && designDto.names.length > 0) {
        for (const nameValue of designDto.names) {
          const newName = this.designNameRepository.create({
            value: nameValue.trim(),
            design: newDesign,
          });
          await this.designNameRepository.save(newName);
          newDesign.names?.push(newName);
        }
      }

      const item = await this.designRepository.save(newDesign);
      return item;
    } catch (error) {
      this.logger.error(`Failed to add the new design: ${error}`);
      throw new NotFoundException(`Failed to add the new design: ${error}`);
    }
  }

  async deleteDesign(id: number): Promise<void> {
    try {
      const existingDesign = await this.designRepository.findOneOrFail({
        where: {
          id: id,
        },
        relations: ['names'],
      });

      await this.designNameRepository.delete({
        design: existingDesign,
      });

      const result = await this.designRepository.delete(id);
      if (result.affected === 0) {
        throw new NotFoundException(`Design with ID ${id} not found`);
      }
      return;
    } catch (error) {
      throw new NotFoundException(`Design with ID ${id} not found`);
    }
  }

  async editDesign(
    id: number,
    design: string,
    names?: string[],
  ): Promise<DesignEntity> {
    try {
      const existingDesign = await this.designRepository.findOneOrFail({
        where: {
          id: id,
        },
        relations: ['names'],
      });

      if (names) {
        await this.designNameRepository.delete({
          design: existingDesign,
        });
        existingDesign.names = [];
        for (const nameValue of names) {
          const newName = this.designNameRepository.create({
            value: nameValue.trim(),
            design: existingDesign,
          });

          await this.designNameRepository.save(newName);
          existingDesign.names?.push(newName);
        }
      }

      existingDesign.design = design.trim();

      const updatedDesign = await this.designRepository.save(existingDesign);
      // this.dataService.updateDesigns();
      return updatedDesign;
    } catch (error) {
      this.logger.error(JSON.stringify(error));
      throw new NotFoundException(`Design with ID ${id} not found`);
    }
  }

  async deleteColor(id: number): Promise<void> {
    try {
      const existingColor = await this.colorRepository.findOneOrFail({
        where: {
          id: id,
        },
        relations: ['names'],
      });

      await this.colorNameRepository.delete({
        color: existingColor,
      });

      const result = await this.colorRepository.delete(id);
      if (result.affected === 0) {
        throw new NotFoundException(`Color with ID ${id} not found`);
      }
      return;
    } catch (error) {
      throw new NotFoundException(`Color with ID ${id} not found`);
    }
  }
}
