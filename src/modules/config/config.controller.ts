// src/colors/controllers/color.controller.ts
import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  Post,
  Put,
  Res,
} from '@nestjs/common';
import { Response } from 'express';

import { ConfigService as ConfigService } from './config.service';
import { ColorDto, CombinedColorDto } from './dto/color.dto';
import { ApiTags } from '@nestjs/swagger';
import { CombinedDesignDto } from './dto/design.dto';
import { DesignEntity } from '../../entities/design.entity';
import { GoogleDriveService } from '../utils/google-drive.service';
import { RoleType } from '../../common/constants';
import { Auth } from '../../common/decorators';

@Controller('config')
@ApiTags('config')
export class ConfigController {
  constructor(
    private readonly configService: ConfigService,
    private readonly googleDriveService: GoogleDriveService,
  ) {}

  @Get('getAllColors')
  async getAllColors(): Promise<CombinedColorDto[]> {
    const colors = await this.configService.getAllColors();

    return colors.map((color) => ({
      colorInfo: {
        id: color.id,
        color: color.color,
        code: color.code,
      },
      colorNameInfo: color.names?.map((name) => ({
        id: name.id,
        value: name.value,
      })),
    }));
  }

  @Get('getAllDesigns')
  async getAllDesigns(): Promise<CombinedDesignDto[]> {
    const designs = await this.configService.getAllDesign();

    return designs.map((design) => ({
      designInfo: {
        id: design.id,
        design: design.design,
      },
      designNameInfo: design.names?.map((name) => ({
        id: name.id,
        value: name.value,
      })),
    }));
  }

  @Post('addColor')
  @Auth([RoleType.ADMIN])
  async addColor(@Body() colorDto: any): Promise<ColorDto> {
    return await this.configService.addColor(colorDto);
  }

  @Post('addDesign')
  @Auth([RoleType.ADMIN])
  async addDesign(@Body() designDto: any): Promise<DesignEntity> {
    return await this.configService.addDesign(designDto);
  }

  @Put('editColor/:id')
  @Auth([RoleType.ADMIN])
  async editColor(
    @Param('id') id: number,
    @Body() colorDto,
  ): Promise<ColorDto> {
    return await this.configService.editColor(
      id,
      colorDto.item.color,
      colorDto.item.code,
      colorDto.item.names,
    );
  }

  @Put('editDesign/:id')
  @Auth([RoleType.ADMIN])
  async editDesign(
    @Param('id') id: number,
    @Body() designDto,
  ): Promise<DesignEntity> {
    return await this.configService.editDesign(
      id,
      designDto.item.design,
      designDto.item.names,
    );
  }

  @Delete('editColor/:id')
  @Auth([RoleType.ADMIN])
  async deleteColor(@Param('id') id: number): Promise<void> {
    await this.configService.deleteColor(id);
  }

  @Delete('editDesign/:id')
  @Auth([RoleType.ADMIN])
  async deleteDesign(@Param('id') id: number): Promise<void> {
    await this.configService.deleteDesign(id);
  }

  @Get('downloadSvg')
  async downloadSvg(@Res() res: Response) {
    const svgContent = await this.googleDriveService.loadSvgFile();
    if (!svgContent) {
      return res.status(404).send('SVG file not found');
    }
    res.setHeader('Content-Type', 'image/svg+xml');
    res.send(svgContent);
  }
}
