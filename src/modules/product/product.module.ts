import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';

import { ColorEntity } from '../../entities/color.entity';
import { ColorNameEntity } from '../../entities/color-name.entity';
import { SettingModule } from '../config/config.module';
import { DesignEntity } from '../../entities/design.entity';
import { DesignNameEntity } from '../../entities/design-name.entity';
import { ProductEntity } from '../../entities/product.entity';
import { ProductDesignEntity } from '../../entities/product-design.entity';
import { ConfigService } from '../config/config.service';
import { ConfigEntity } from '../../entities/config.entity';
import { ProductService } from './services/product.service';
import { ProductController } from './controllers/product.controller';
import { AmazonService } from './services/amazon.service';
import { EtsyService } from './services/etsy.service';
import { ShopifyService } from './services/shopify.service';
import { CountryService } from '../utils/country.util';
import { OrderEntity } from '../../entities/order.entity';
import { SkuModule } from '../sku/sku.module';
import { SoldProductEntity } from '../../entities/sold_product.entity';
import { BullModule } from '@nestjs/bull';

@Module({
  imports: [
    TypeOrmModule.forFeature([
      ConfigEntity,
      OrderEntity,
      ProductEntity,
      ProductDesignEntity,
      ColorEntity,
      ColorNameEntity,
      DesignEntity,
      DesignNameEntity,
      SoldProductEntity,
    ]),
    SettingModule,
    SkuModule,
    BullModule.registerQueue({
      name: 'Amazon',
    }),
    BullModule.registerQueue({
      name: 'Shopify',
    }),
  ],
  providers: [
    CountryService,
    ProductService,
    ConfigService,
    ShopifyService,
    AmazonService,
    EtsyService,
  ],
  exports: [ProductService],
  controllers: [ProductController],
})
export class ProductModule {}
