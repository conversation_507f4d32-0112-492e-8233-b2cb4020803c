import { ApiProperty } from '@nestjs/swagger';

import { AbstractDto } from '../../../common/dto/abstract.dto';
import { ProductEntity } from '../../../entities/product.entity';
import { ProductDesignDto } from './product-design.dto';

export class ProductDto extends AbstractDto {
  @ApiProperty()
  externalProductId?: string;

  @ApiProperty()
  source!: string;

  @ApiProperty()
  quantity?: number;

  @ApiProperty()
  FNSKU?: string;

  @ApiProperty()
  ASIN?: string;

  @ApiProperty()
  inventory_item_id?: string;

  @ApiProperty()
  inventory_management?: string;

  @ApiProperty()
  amazon_quantity?: number;

  @ApiProperty()
  etsy_quantity?: number;

  @ApiProperty()
  shopify_quantity?: number;

  @ApiProperty()
  title?: string;

  @ApiProperty()
  status!: string;

  @ApiProperty()
  size?: number;

  @ApiProperty()
  sku?: string;

  @ApiProperty()
  style?: string;

  @ApiProperty()
  color?: string;

  @ApiProperty()
  design?: string;

  @ApiProperty()
  note?: string;

  @ApiProperty()
  oosDate?: Date | null;

  @ApiProperty()
  checkingTime?: Date | null;

  @ApiProperty()
  checked?: boolean;

  @ApiProperty()
  product_designs?: ProductDesignDto[];

  @ApiProperty()
  amazon_skus?: string;

  @ApiProperty()
  etsy_offerings?: string;

  constructor(productEntity: ProductEntity) {
    super(productEntity);
    this.createdAt = productEntity.externalCreatedAt ?? new Date();
    this.source = productEntity.source ?? '-';
    this.quantity = productEntity.quantity;
    this.status = productEntity.status ?? '';
    this.sku = productEntity.sku;
    this.externalProductId = productEntity.externalProductId;
    this.size = productEntity.size;
    this.color = productEntity.color;
    this.design = productEntity.design;
    this.product_designs = productEntity.product_designs;
    this.style = productEntity.style;
    this.shopify_quantity = productEntity.shopify_quantity;
    this.amazon_quantity = productEntity.amazon_quantity;
    this.etsy_quantity = productEntity.etsy_quantity;
    this.note = productEntity.note;
    this.inventory_item_id = productEntity.inventory_item_id;
    this.oosDate = productEntity.oosDate;
    this.inventory_management = productEntity.inventory_management;
    this.checked = productEntity.checked;
    this.checkingTime = productEntity.checkingTime;
    this.amazon_skus = productEntity.amazon_skus;
    this.etsy_offerings = productEntity.etsy_offerings;
  }
}
