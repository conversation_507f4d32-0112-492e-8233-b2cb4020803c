import {
  Body,
  Controller,
  Get,
  HttpException,
  Post,
  Put,
  Query,
  UploadedFile,
  UseInterceptors,
} from '@nestjs/common';
import { FileInterceptor } from '@nestjs/platform-express';
import { ApiTags, ApiOkResponse } from '@nestjs/swagger';
import { RoleType } from '../../../common/constants';
import { Auth } from '../../../common/decorators';
import { PageDto } from '../../../common/dto/page.dto';
import { PaginateProductsOptionsDto } from '../../orders/dto/paginate-product-options.dto';
import { ProductDto } from '../dto/product.dto';
import { ProductService } from '../services/product.service';
import { ShopifyService } from '../services/shopify.service';
import { AmazonService } from '../services/amazon.service';
import { EtsyService } from '../services/etsy.service';

@Controller('product')
@ApiTags('product')
export class ProductController {
  constructor(
    private readonly productService: ProductService,
    private readonly etsyService: EtsyService,
    private readonly amazonService: AmazonService,
    private readonly shopifyService: ShopifyService,
  ) {}

  @Get('etsy/auth')
  async etsyAuth() {
    return this.etsyService.requestAuthorizationCode();
  }

  @Get('etsy/token')
  async etsyToken(@Query('code') code: string) {
    return this.etsyService.requestAccessToken(code);
  }

  /**
   * Fetch Amazon orders for the last month (optimized)
   */
  @Post('amazon/orders/last-month')
  @Auth([RoleType.ADMIN])
  async fetchLastMonthAmazonOrders() {
    try {
      await this.amazonService.fetchLastMonthOrders();
      return {
        success: true,
        message:
          'Started fetching last month Amazon orders with optimized rate limiting',
      };
    } catch (error: any) {
      throw new HttpException(
        `Error starting Amazon order fetch: ${error.message}`,
        500,
      );
    }
  }

  /**
   * Fetch Amazon orders for a custom date range (optimized)
   */
  @Post('amazon/orders/date-range')
  @Auth([RoleType.ADMIN])
  async fetchAmazonOrdersDateRange(
    @Body() body: { startDate: string; endDate: string },
  ) {
    try {
      const startDate = new Date(body.startDate);
      const endDate = new Date(body.endDate);

      if (isNaN(startDate.getTime()) || isNaN(endDate.getTime())) {
        throw new HttpException('Invalid date format', 400);
      }

      await this.amazonService.fetchOrdersOptimized(startDate, endDate);
      return {
        success: true,
        message: `Started fetching Amazon orders from ${startDate.toISOString()} to ${endDate.toISOString()}`,
      };
    } catch (error: any) {
      throw new HttpException(
        `Error starting Amazon order fetch: ${error.message}`,
        500,
      );
    }
  }

  /**
   * Clear Amazon queue and restart processing
   */
  @Post('amazon/queue/clear')
  @Auth([RoleType.ADMIN])
  async clearAmazonQueue() {
    try {
      await this.amazonService.clearQueue();
      return {
        success: true,
        message: 'Amazon queue cleared successfully',
      };
    } catch (error: any) {
      throw new HttpException(
        `Error clearing Amazon queue: ${error.message}`,
        500,
      );
    }
  }

  /**
   * Upload a file and process it
   * @param file - Uploaded Excel file
   * @returns Success message
   */
  @Post('uploadFile')
  @Auth([RoleType.ADMIN])
  @ApiOkResponse({ description: 'Successfully Uploaded' })
  @UseInterceptors(FileInterceptor('excelFile'))
  async uploadFile(@UploadedFile() file: Express.Multer.File) {
    return this.shopifyService.uploadFile(file);
  }

  /**
   * Update inventory from Excel file
   * @param file - Uploaded Excel file
   * @returns Success message
   */
  @Post('updateInventoryFromExcel')
  @Auth([RoleType.ADMIN])
  @ApiOkResponse({ description: 'Successfully Updated' })
  @UseInterceptors(FileInterceptor('excelFile'))
  async updateInventoryFromExcel(
    @UploadedFile() file: Express.Multer.File,
  ): Promise<boolean> {
    return this.shopifyService.updateInventoryFromExcel(file);
  }

  /**
   * Update multiple products
   * @param updateData - Data for updating products
   * @returns Updated products
   */
  @Put('updateProducts')
  @Auth([RoleType.ADMIN, RoleType.USER])
  async updateProducts(
    @Body()
    updateData: {
      items: { sku: string; inventory_item_id: string; qty: number }[];
    },
  ): Promise<ProductDto[]> {
    const updatedProducts = await this.shopifyService.updateProducts(
      updateData.items,
    );

    if (!updatedProducts) {
      throw new HttpException('Products not updated', 500);
    }

    this.amazonService.updateListingQuantities(updatedProducts);
    // this.etsyService.updateListingQuantities(updatedProducts);

    return updatedProducts;
  }

  /**
   * Upload a file and process it
   * @param file - Uploaded Excel file
   * @returns Success message
   */
  @Post('updateAmazonInventory')
  // @Auth([RoleType.ADMIN])
  @ApiOkResponse({ description: 'Successfully Updated' })
  @UseInterceptors(FileInterceptor('excelFile'))
  async updateAmazonInventory(@UploadedFile() file: Express.Multer.File) {
    return this.amazonService.updateInventoryFromExcel(file);
  }

  /**
   * Get all products with pagination
   * @param pageOptions - Pagination options
   * @param data - Additional query data
   * @returns Paginated products
   */
  @Get('getAll')
  @Auth([RoleType.ADMIN, RoleType.USER])
  async getAll(
    @Query() pageOptions: PaginateProductsOptionsDto,
    @Query('data') data?: string,
  ): Promise<PageDto<ProductDto>> {
    return this.productService.getAllProducts(pageOptions, data);
  }

  /**
   * Get a product by external ID
   * @param externalProductId - External product ID
   * @returns Product details
   */
  @Get('productByExternalId')
  @Auth([RoleType.ADMIN, RoleType.USER])
  async getProductByExternalId(
    @Query('external_product_id') externalProductId: string,
  ): Promise<ProductDto[]> {
    const product =
      await this.productService.getProductsByExternalId(externalProductId);

    if (!product) {
      throw new HttpException('Product not found', 404);
    }

    return product;
  }

  /**
   * Get low stock products with pagination
   * @param pageOptions - Pagination options
   * @param data - Additional query data
   * @returns Paginated low stock products
   */
  @Get('lowStock')
  @Auth([RoleType.ADMIN, RoleType.USER])
  async getLowStockProducts(
    @Query() pageOptions: PaginateProductsOptionsDto,
    @Query('data') data?: string,
  ): Promise<PageDto<ProductDto>> {
    return this.productService.getLowStockProducts(pageOptions, data);
  }

  /**
   * Get sold products with pagination
   * @param paginateOptions - Pagination options
   * @param data - Additional query data
   * @returns Paginated sold products
   */
  @Get('sold-products')
  @Auth([RoleType.ADMIN, RoleType.USER])
  async getSoldProducts(
    @Query() paginateOptions: PaginateProductsOptionsDto,
    @Query('data') data?: string,
  ): Promise<PageDto<any>> {
    return this.productService.getSoldProducts(paginateOptions, data);
  }

  /**
   * Get products by out-of-stock date with pagination
   * @param pageOptions - Pagination options
   * @returns Paginated products by OOS date
   */
  @Get('by-oos-date')
  @Auth([RoleType.ADMIN, RoleType.USER])
  async getProductsByOosDate(
    @Query() pageOptions: PaginateProductsOptionsDto,
  ): Promise<PageDto<ProductDto>> {
    try {
      return await this.productService.getProductsByOosDate(pageOptions);
    } catch (error) {
      throw new HttpException('Product not found', 404);
    }
  }

  /**
   * Get products sorted by style, color, and size
   * @param pageOptions - Pagination options
   * @param data - Additional query data
   * @returns Paginated and sorted products
   */
  @Get('sorted-products')
  @Auth([RoleType.ADMIN, RoleType.USER])
  async getSortedProducts(
    @Query() pageOptions: PaginateProductsOptionsDto,
    @Query('data') data?: string,
  ): Promise<PageDto<ProductDto>> {
    return this.productService.getSortedProducts(pageOptions, data);
  }
}
