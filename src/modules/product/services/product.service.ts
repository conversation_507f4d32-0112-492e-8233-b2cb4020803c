import {
  Injectable,
  Logger,
  Inject,
  OnModuleInit,
  HttpException,
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import {
  Brackets,
  Equal,
  <PERSON>ike,
  IsNull,
  <PERSON>ThanOrEqual,
  <PERSON><PERSON>han,
  Not,
  Repository,
} from 'typeorm';
import { Cron, CronExpression } from '@nestjs/schedule';
import { RingInfo } from '../../../common/interfaces';
import { FindProductResult } from '../types/FindProductResult';
import { CACHE_MANAGER } from '@nestjs/cache-manager';
import { Cache } from 'cache-manager';
import { ProductDto } from '../dto/product.dto';
import { PageDto } from '../../../common/dto/page.dto';
import { PaginateProductsOptionsDto } from '../../orders/dto/paginate-product-options.dto';
import { SkuService } from '../../sku/sku.service';
import { addDays } from 'date-fns';
import { SoldProductEntity } from '../../../entities/sold_product.entity';
import { convertToSQLDateTime } from '../../utils/date.util';
import { ApiConfigService } from '../../../common/services/api-config.service';
import { subDays, subMonths } from 'date-fns';
import { ProductEntity } from '../../../entities/product.entity';
import { ProductDesignEntity } from '../../../entities/product-design.entity';

@Injectable()
export class ProductService implements OnModuleInit {
  private readonly logger = new Logger(ProductService.name);

  constructor(
    private readonly appConfig: ApiConfigService,
    @InjectRepository(ProductEntity)
    private readonly productRepository: Repository<ProductEntity>,
    @InjectRepository(ProductDesignEntity)
    private readonly productDesignRepository: Repository<ProductDesignEntity>,
    @InjectRepository(SoldProductEntity)
    private readonly soldProductRepository: Repository<SoldProductEntity>,
    @Inject(CACHE_MANAGER) private cacheService: Cache,
    private readonly skuService: SkuService,
  ) {}
  onModuleInit() {
    // this.getProductsWithMatchedColors();
    // this.updateMissingProducts();
  }

  /**
   * Cron Jobs - Scheduled Functions
   */

  // Update missing products daily at 3 AM
  @Cron(CronExpression.EVERY_DAY_AT_3AM)
  async updateMissingProducts(): Promise<void> {
    this.logger.log('Starting updateMissingProducts process');
    try {
      const items = await this.productRepository
        .createQueryBuilder('product')
        .where("(product.design IS NULL OR product.design = '')")
        .orWhere("(product.color IS NULL OR product.color = '')")
        .orWhere("(product.style IS NULL OR product.style = '')")
        .orWhere('(product.size IS NULL OR product.size = 0)')
        .getMany();

      this.logger.log(`Found ${items.length} products with missing attributes`);

      for (const item of items) {
        try {
          this.logger.log(`Processing item with SKU: ${item.sku}`);
          const itemSpecification: RingInfo = await this.skuService.extractSku(
            item.sku ?? '',
          );

          let changes: string[] = [];

          if (
            (item.design == '' || item.design == null) &&
            item.design != itemSpecification.design
          ) {
            item.design = itemSpecification.design;
            changes.push(`design: ${itemSpecification.design}`);
          }

          this.logger.verbose(
            `${item.sku} | item.color: ${item.color} | itemSpecification.color: ${itemSpecification.color}`,
          );

          if (
            (item.style == '' || item.style == null) &&
            item.style != itemSpecification.style
          ) {
            item.style = itemSpecification.style;
            changes.push(`style: ${itemSpecification.style}`);
          }

          if (
            (item.color == '' || item.color == null) &&
            item.color != itemSpecification.color
          ) {
            item.color = itemSpecification.color;
            changes.push(`color: ${itemSpecification.color}`);
          }

          if (
            (item.size == 0 || item.size == null) &&
            item.size != itemSpecification.size
          ) {
            if (itemSpecification.size < 20) {
              item.size = itemSpecification.size;
              changes.push(`size: ${itemSpecification.size}`);
            }
          }

          if (changes.length > 0) {
            await this.productRepository.save(item);
            this.logger.log(
              `Updated product with SKU: ${item.sku} - Changes: ${changes.join(
                ', ',
              )}`,
            );
          }
        } catch (error) {
          this.logger.error(
            `Error processing item with SKU: ${item.sku} - Error: ${error}`,
          );
        }
      }
      this.logger.log('Completed updateMissingProducts process');
    } catch (error) {
      this.logger.error(
        `Failed during updateMissingProducts process - Error: ${error}`,
      );
    }
  }

  // Get products with matched colors every day at 4 AM
  @Cron(CronExpression.EVERY_DAY_AT_4AM)
  async getProductsWithMatchedColors(): Promise<any[]> {
    try {
      const matchedProducts = await this.productRepository
        .createQueryBuilder('o')
        .leftJoin('color_names', 'cn', 'o.color ILIKE cn.value')
        .leftJoin('colors', 'c', 'cn.color_id = c.id')
        .select([
          'o.id AS productId',
          'o.color AS productColor',
          'c.color AS matchedColor',
        ])
        .where('cn.value IS NOT NULL')
        .getRawMany();

      this.logger.verbose(`Matched Products: ${matchedProducts.length}`);

      for (const product of matchedProducts) {
        if (product.productcolor != product.matchedcolor) {
          try {
            await this.updateProductColor(
              product.productid,
              product.productcolor,
              product.matchedcolor,
            );
          } catch (error) {
            this.logger.error(
              `Error updating product ID ${product.productid}:`,
              error,
            );
          }
        }
      }

      return matchedProducts;
    } catch (error) {
      this.logger.error('Error fetching matched products:', error);
      throw new Error('Error fetching matched products');
    }
  }

  async updateProduct(
    product: ProductEntity,
    updateData: Partial<ProductEntity>,
  ): Promise<void> {
    try {
      if (!product) {
        throw new HttpException('Product not found', 404);
      }

      // Fetch all products with the same style, color, and size
      const products = await this.productRepository.find({
        where: {
          style: product.style,
          color: product.color,
          size: product.size,
        },
      });

      if (!products || products.length === 0) {
        throw new HttpException(
          'No products found with the given attributes',
          404,
        );
      }

      for (const p of products) {
        this.logger.debug(`Updating product ${p.sku}`);
        if (updateData.sku) {
          p.sku = updateData.sku;
        }
        if (updateData.FNSKU) {
          p.FNSKU = updateData.FNSKU;
        }
        if (updateData.ASIN) {
          p.ASIN = updateData.ASIN;
        }

        try {
          const updatedProduct = await this.productRepository.save(p);
          this.logger.debug(
            `Updated product ${updatedProduct.sku} to ${updateData.sku} | ${updateData.ASIN} | ${updateData.FNSKU}`,
          );
        } catch (error) {
          this.logger.error(`Failed to update product ${p.sku}:`, error);
          throw new HttpException(`Failed to update product ${p.sku}`, 500);
        }
      }
    } catch (error) {
      this.logger.error(`Error updating product ${product.sku}:`, error);
      throw new HttpException('Failed to update product', 500);
    }
  }

  async getSoldProducts(
    paginateOptions: PaginateProductsOptionsDto,
    data?: string,
    onlyUnsaved = false,
  ): Promise<PageDto<SoldProductEntity>> {
    const { take: PAGE_SIZE, page: pageNumber } = paginateOptions;
    const jsonData = JSON.parse(data ?? '');

    const searchQuery = jsonData.query;

    const sortKey = paginateOptions.orderBy ?? 'total_sales';
    const sortOrder = paginateOptions.order;

    const queryBuilder = this.soldProductRepository.createQueryBuilder('s');

    const date = new Date(jsonData.date);
    const endDate = addDays(date, 1);

    queryBuilder
      .where('sales_date >= :start and sales_date < :end', {
        start: convertToSQLDateTime(date, false, this.appConfig.isDevelopment),
        end: convertToSQLDateTime(endDate, true, this.appConfig.isDevelopment),
      })
      .leftJoinAndSelect(
        'products',
        'p',
        's.inventory_item_id = p.inventory_item_id',
      )
      .addSelect('p.shopify_quantity as local_inventory');

    if (onlyUnsaved) {
      queryBuilder.andWhere(`saved=false`);
    }

    if (searchQuery) {
      const keywords = searchQuery.split(' ');
      queryBuilder.andWhere(
        new Brackets((subQb) => {
          subQb.andWhere([
            { style: ILike(`%${keywords[0]}%`) },
            { color: ILike(`%${keywords[0]}%`) },
            {
              size: isNaN(parseInt(keywords[0]))
                ? -1
                : Equal(parseInt(keywords[0])),
            },
          ]);

          for (let i = 1; i < keywords.length; i++) {
            subQb.andWhere([
              { style: ILike(`%${keywords[i]}%`) },
              { color: ILike(`%${keywords[i]}%`) },
              {
                size: isNaN(parseInt(keywords[i]))
                  ? -1
                  : Equal(parseInt(keywords[i])),
              },
            ]);
          }
        }),
      );
    }

    queryBuilder.orderBy(sortKey, sortOrder);

    const total = await queryBuilder.getRawMany();

    let [rawQuery, params] = queryBuilder.getQueryAndParameters();
    if (PAGE_SIZE > 0) {
      rawQuery += ` LIMIT ${PAGE_SIZE}`;
      rawQuery += ` OFFSET ${(pageNumber - 1) * PAGE_SIZE}`;
    }

    const rawResult: [] = await this.soldProductRepository.query(
      rawQuery,
      params,
    );

    const pageCount = Math.ceil(total.length / PAGE_SIZE);
    const response = new PageDto(rawResult, {
      hasNextPage: pageNumber < pageCount,
      hasPreviousPage: pageNumber == 1 ? false : true,
      itemCount: total.length,
      page: pageNumber,
      take: PAGE_SIZE,
      pageCount: pageCount,
    });

    return response;
  }

  async markAsSaved(id: string): Promise<void> {
    await this.soldProductRepository.update(id, { saved: true });
  }

  async setAllProductsOosDate(): Promise<void> {
    try {
      const currentDate = new Date();

      await this.updateProductsWithPositiveQuantity();
      await this.updateProductsWithZeroOrNegativeQuantity(currentDate);
    } catch (error) {
      this.logger.error('Error setting oosDate for all products', error);
    }
  }

  async deleteUnusedItems(): Promise<void> {
    const result = await this.productRepository.delete({
      title: ILike('-'),
      source: ILike('shopify'),
    });
    this.logger.debug(`Deleted ${result.affected} unused items`);
  }

  private async updateProductsWithPositiveQuantity(): Promise<void> {
    const result = await this.productRepository.update(
      { shopify_quantity: MoreThan(0), oosDate: Not(IsNull()) },
      { oosDate: null },
    );
    this.logger.debug(
      `Products with positive quantity updated: ${result.affected}`,
    );
  }

  private async updateProductsWithZeroOrNegativeQuantity(
    currentDate: Date,
  ): Promise<void> {
    const result = await this.productRepository.update(
      { shopify_quantity: LessThanOrEqual(0), oosDate: IsNull() },
      { oosDate: currentDate, updatedAt: currentDate },
    );
    this.logger.debug(
      `Products with zero or negative quantity updated: ${result.affected}`,
    );
  }

  async findProductById(id: string): Promise<ProductEntity | null> {
    return this.productRepository.findOne({ where: { externalProductId: id } });
  }

  async updateAmazonDate(id: string): Promise<void> {
    await this.productRepository.update(
      { externalProductId: id },
      { amazonUpdatedTime: new Date() },
    );
  }

  getAmazonProducts(): Promise<ProductEntity[]> {
    const yesterday = new Date();
    yesterday.setDate(yesterday.getDate() - 1);

    return this.productRepository
      .createQueryBuilder('product')
      .where('product.amazon_skus IS NOT NULL')
      .andWhere('product.amazon_skus != :empty', { empty: '' })
      .andWhere(
        '(product.amazonUpdatedTime < :yesterday OR product.amazonUpdatedTime IS NULL)',
        { yesterday },
      )
      .getMany();
  }

  // Get products with oosDate greater than specified date every day at 9 AM
  async getProductsByOosDate(
    paginateOptions?: PaginateProductsOptionsDto,
  ): Promise<any> {
    try {
      const productsQuery = this.productRepository
        .createQueryBuilder('p')
        .select([
          'p.inventory_item_id',
          'p.externalProductId',
          'p.style',
          'p.color',
          'p.size',
          'p.shopify_quantity',
          'p.updated_at',
          'p.oosDate',
        ]);

      productsQuery
        .where(`p.style != ''`)
        .andWhere(`p.color != ''`)
        .andWhere('p.size > 0')
        .andWhere('p.shopify_quantity <= 0')
        .andWhere(`p.inventory_management ilike 'shopify'`)
        .andWhere('p.inventory_item_id IS NOT NULL')
        .andWhere('p.oosDate IS NOT NULL');

      const { page = 1, take = 50 } = paginateOptions || {};
      const skip = (page - 1) * take;

      productsQuery.orderBy('p.oosDate', 'DESC').take(take).skip(skip);

      const [data, total] = await productsQuery.getManyAndCount();

      const totalPages = Math.ceil(total / take);
      const hasNextPage = page < totalPages;
      const hasPreviousPage = page > 1;

      const response = {
        data,
        meta: {
          hasNextPage,
          hasPreviousPage,
          itemCount: total,
          page,
          pageCount: totalPages,
          take,
        },
      };

      return response;
    } catch (e) {
      this.logger.error('Error fetching products by oosDate', e);
      throw e;
    }
  }

  /**
   * Helper Functions
   */

  // Update the color of a product
  private async updateProductColor(
    productId: number,
    productColor: string,
    newColor: string,
  ): Promise<void> {
    try {
      await this.productRepository
        .createQueryBuilder()
        .update(ProductEntity)
        .set({ color: newColor })
        .where('id = :productId', { productId })
        .execute();

      this.logger.debug(
        `Updated Product ID ${productId} with color ${productColor} to ${newColor}`,
      );
    } catch (error) {
      this.logger.error(`Error updating Product ID ${productId}:`, error);
      throw new Error(`Error updating Product ID ${productId}`);
    }
  }

  /**
   * Other Functions
   */
  /**
   * Other Functions
   */

  // Find a product by SKU
  async findProduct({
    sku,
    justSKU = false,
    amazonUpdate = false,
    noCache = false,
  }: {
    sku: string;
    justSKU?: boolean;
    amazonUpdate?: boolean;
    noCache?: boolean;
  }): Promise<FindProductResult> {
    const cacheKey = `${sku}/${justSKU}`;
    const cachedData: any = await this.cacheService.get(cacheKey);
    const productInfo: RingInfo = await this.skuService.extractSku(sku);

    if (amazonUpdate) {
      let product: ProductEntity | null = null;
      product = await this.productRepository.findOne({
        where: [
          {
            style: ILike(productInfo.style),
            size: Number(productInfo.size), // Convert size to number
            color: ILike(productInfo.color),
          },
          { sku: ILike(sku) },
        ],
        relations: ['product_designs'],
      });

      if (product) {
        // Check if the amazon_skus string is not null or undefined and doesn't include the current sku
        if (product.amazon_skus) {
          if (!product.amazon_skus.includes(sku)) {
            const updatedSkus = `${product.amazon_skus},${sku}`;
            await this.productRepository.update(
              { id: product.id, sku: product.sku }, // Use all primary keys
              { amazon_skus: updatedSkus },
            );
          }
        } else {
          await this.productRepository.update(
            { id: product.id, sku: product.sku }, // Use all primary keys
            { amazon_skus: sku },
          );
        }
        this.logger.debug(
          `${product.id}\namazon_skus:${product.amazon_skus}\nsku:${sku}`,
        );
      }
    }

    if (cachedData?.product && cachedData?.productInfo && !noCache) {
      return cachedData;
    }

    let product: ProductEntity | null = null;

    try {
      if (justSKU) {
        product = await this.productRepository.findOne({
          where: { sku: ILike(sku) },
          relations: ['product_designs'],
        });
        await this.cacheService.set(cacheKey, { product, productInfo }, 1800);
      } else {
        product = await this.productRepository.findOne({
          where: [
            {
              style: ILike(productInfo.style),
              size: Number(productInfo.size), // Convert size to number
              color: ILike(productInfo.color),
            },
            { sku: ILike(sku) },
          ],
          relations: ['product_designs'],
        });
        await this.cacheService.set(cacheKey, { product, productInfo }, 600);
      }
    } catch (error: any) {
      this.logger.error(`findProduct error => ${sku}`, error);
      product = null;
    }

    return { product, productInfo };
  }

  // Handle selling an item by SKU
  async sellItem(itemSku: string): Promise<boolean> {
    try {
      await new Promise((resolve) => setTimeout(resolve, 500));
      const ii = await this.findProduct({ sku: itemSku });

      if (ii.product) {
        // Update quantity here if needed
        return true;
      } else {
        if (
          ii.productInfo.size > 0 &&
          ii.productInfo.color &&
          ii.productInfo.style
        ) {
          const pp = await this.productRepository.findOne({
            where: [
              {
                size: ii.productInfo.size,
                color: ii.productInfo.color,
                style: ii.productInfo.style,
              },
            ],
          });
          if (!pp) {
            try {
              const inserted = this.productRepository.create({
                externalProductId: '-',
                externalCreatedAt: new Date(),
                updatedAt: new Date(),
                quantity: 0,
                source: 'Orders',
                status: 'active',
                sku: itemSku,
                size: ii.productInfo.size,
                color: ii.productInfo.color,
                style: ii.productInfo.style,
              });
              this.logger.debug(`add new product | ${itemSku}`);
              await this.productRepository.save(inserted);
            } catch (e) {
              this.logger.error(e);
            }
          }
        }
      }
      return false;
    } catch (error) {
      return false;
    }
  }

  // Update Shopify product
  async updateShopify(product: any, shopifyItem: any): Promise<void> {
    this.logger.warn(`shopifyItem.sku: ${shopifyItem.sku}`);

    if (shopifyItem.sku.startsWith('G-') || product.title.includes('[DEV]')) {
      return;
    }

    try {
      const ii = await this.findProduct({ sku: shopifyItem.sku });
      if (
        product.status.toLowerCase() != 'active' ||
        product.title.includes('[DEV]')
      ) {
        if (ii.product) {
          this.logger.warn(`delete product | ${shopifyItem.sku}`);
          await this.productRepository
            .createQueryBuilder()
            .delete()
            .from(ProductEntity)
            .where('sku = :sku', { sku: shopifyItem.sku })
            .execute();
        }
        return;
      }

      const itemSpecification: RingInfo = await this.skuService.extractSku(
        shopifyItem.sku ?? '',
      );

      if (itemSpecification.design == null || itemSpecification.design == '') {
        if (ii.product) {
          const hasQuantityChanged =
            ii.product.shopify_quantity !== shopifyItem.inventory_quantity;

          if (
            hasQuantityChanged ||
            ii.product.sku != shopifyItem.sku ||
            ii.product.title != product.title ||
            ii.product.externalProductId != product.id
          ) {
            if (!ii.product.oosDate && shopifyItem.inventory_quantity <= 0) {
              ii.product.oosDate = subDays(new Date(), 1);
              this.logger.warn(
                `oosDate: ${ii.product.sku} | ${ii.product.oosDate} | local_quantity (${ii.product.shopify_quantity})`,
              );
            } else if (
              shopifyItem.inventory_quantity !== ii.product.shopify_quantity &&
              shopifyItem.inventory_quantity > 0
            ) {
              ii.product.oosDate = null; // Reset oosDate to null
            }

            ii.product.shopify_quantity = shopifyItem.inventory_quantity;
            ii.product.status = product.status;
            ii.product.source = 'Shopify';
            ii.product.title = product.title;
            ii.product.sku = shopifyItem.sku ?? '';
            ii.product.externalProductId = `${product.id}`;
            ii.product.inventory_item_id = `${shopifyItem.inventory_item_id}`;
            ii.product.inventory_management = `${shopifyItem.inventory_management}`;
            ii.product.design = `${itemSpecification.design}`;
            ii.product.updatedAt = new Date();
            if (ii.product.color !== ii.productInfo.color) {
              ii.product.color = ii.productInfo.color;
            }
            if (ii.product.design !== ii.productInfo.design) {
              ii.product.design = ii.productInfo.design;
            }
            await this.productRepository.save(ii.product);
            this.logger.debug(
              `update product | ${shopifyItem.sku} | quantity: ${shopifyItem.inventory_quantity}`,
            );
          } else {
            this.logger.warn(
              `no changes | ${shopifyItem.sku} | quantity: ${shopifyItem.inventory_quantity} | id: ${ii.product.externalProductId}  | style: ${ii.product.style} | color: ${ii.product.color} | size: ${ii.product.size}`,
            );
          }
        } else {
          const inserted = this.productRepository.create({
            sku: shopifyItem.sku ?? '',
            externalProductId: `${product.id}`,
            externalCreatedAt: shopifyItem.created_at,
            shopify_quantity: shopifyItem.inventory_quantity,
            inventory_item_id: `${shopifyItem.inventory_item_id}`,
            inventory_management: `${shopifyItem.inventory_management}`,
            updatedAt: new Date(),
            design: `${itemSpecification.design}`,
            source: 'Shopify',
            title: product.title,
            status: product.status,
            size: ii.productInfo.size,
            color: ii.productInfo.color,
            style: ii.productInfo.style,
          });
          await this.productRepository.save(inserted);
          this.logger.debug(`add new product | ${shopifyItem.sku}`);
        }
      } else {
        this.logger.warn(
          `design is not null | ${shopifyItem.sku} | design: ${itemSpecification.design}`,
        );
      }
    } catch (e) {
      this.logger.error(
        `updateShopify error product => ${JSON.stringify(product)}`,
      );
    }
  }

  async getLowStockProducts(
    paginateOptions: PaginateProductsOptionsDto,
    data?: string,
  ): Promise<PageDto<ProductDto>> {
    const cacheKey = JSON.stringify(
      `getAllProducts${JSON.stringify(paginateOptions)}/${data}`,
    );
    const cachedData: any = await this.cacheService.get(cacheKey);
    if (cachedData) {
      this.logger.debug('cachedData');
      return cachedData;
    }
    const queryBuilder = this.productRepository.createQueryBuilder('product');

    if (data) {
      const jsonData = JSON.parse(data);
      const { sortBy, order, minimumQuantity, source } = jsonData;

      const qty = minimumQuantity || -50;

      let quantityField = 'product.shopify_quantity';
      if (source) {
        switch (source) {
          case 'Amazon':
            quantityField = 'product.amazon_quantity';
            break;
          case 'Etsy':
            quantityField = 'product.etsy_quantity';
            break;
          case 'Shopify':
            quantityField = 'product.shopify_quantity';
            break;
        }
      }

      queryBuilder.andWhere(`${quantityField} <= :quantity`, { quantity: qty });

      const isValidSortKey = this.productRepository.metadata.columns.some(
        (column) => column.propertyName === sortBy,
      );

      if (isValidSortKey) {
        queryBuilder.orderBy(`product.${sortBy}`, order.toUpperCase());
      }
    }

    queryBuilder.andWhere(`product.inventory_management ilike 'shopify'`);
    queryBuilder.andWhere('product.inventory_item_id IS NOT NULL');
    queryBuilder.andWhere(`product.style IS NOT NULL and product.style != ''`);
    queryBuilder.andWhere(`product.color IS NOT NULL and product.color != ''`);
    const [items, pageMetaDto] = await queryBuilder.paginate(paginateOptions);
    const response = items.toPageDto<ProductDto>(pageMetaDto);
    return response;
  }

  // Update all Amazon quantities to zero
  async updateAllAmazonQuantitiesToZero(): Promise<void> {
    try {
      await this.productDesignRepository.delete({});
      await this.productRepository.update({}, { amazon_quantity: 0, note: '' });
      const count = await this.productRepository.count();
      this.logger.debug(`Updated ${count} products' amazon_quantity to 0.`);
    } catch (e) {
      this.logger.error('updateAllAmazonQuantitiesToZero error:', e);
    }
  }

  // Update Amazon product
  async updateAmazon(product: any): Promise<void> {
    try {
      const existingProduct = await this.findProduct({
        sku: product.sellerSku,
      });

      const designTitle = existingProduct.productInfo.design?.trim() || '';
      const qty = product.inventoryDetails.fulfillableQuantity;

      this.logger.debug(`${product.sellerSku} | designTitle: ${designTitle}`);

      if (existingProduct.product) {
        if (designTitle === '' || !designTitle) {
          existingProduct.product.amazon_quantity += qty;
          existingProduct.product.note += `/${product.sellerSku}`;
          await this.productRepository.save(existingProduct.product);
        } else {
          let designFound = false;
          for (const design of existingProduct.product.product_designs || []) {
            if (design.title?.trim() === designTitle) {
              design.quantity += qty;
              this.logger.warn(`designFound`);
              await this.productDesignRepository.save(design);
              designFound = true;
              break;
            }
          }
          if (!designFound) {
            this.logger.verbose(`design not Found`);
            const newDesign = this.createProductDesign(
              existingProduct.product,
              designTitle,
              qty,
            );
            existingProduct.product.product_designs?.push(newDesign);
          }
        }
        await this.productRepository.save(existingProduct.product);
      } else {
        const newProduct = this.createProduct(product, qty, designTitle);
        if (designTitle) {
          const newDesign = this.createProductDesign(
            newProduct,
            designTitle,
            qty,
          );
          newProduct.product_designs?.push(newDesign);
        }
        await this.productRepository.save(newProduct);
      }
    } catch (error) {
      this.handleError(error, product);
    }
  }

  // Update Amazon product
  async updateAmazonSKUs(sku: string): Promise<void> {
    try {
      if (!sku || sku === '') {
        return;
      }
      const existingProduct = await this.findProduct({ sku });

      if (existingProduct.product) {
        const amazonSkus: string[] =
          existingProduct.product?.amazon_skus?.split(',') ?? [];

        this.logger.debug(
          `${sku} | ${existingProduct.product.sku} | ${amazonSkus}`,
        );

        if (amazonSkus.find((itemSku) => itemSku === sku)) {
          return;
        } else {
          amazonSkus.push(sku);
          existingProduct.product.amazon_skus = amazonSkus.join(',');
          await this.productRepository.save(existingProduct.product);

          this.logger.verbose(
            `NEW | ${sku} |  ${existingProduct.product?.amazon_skus}`,
          );
        }
      } else {
        const inserted = this.productRepository.create({
          externalProductId: '-',
          externalCreatedAt: new Date(),
          updatedAt: new Date(),
          quantity: 0,
          source: 'Amazon',
          status: 'not_found',
          sku: sku,
          size: existingProduct.productInfo.size,
          color: existingProduct.productInfo.color,
          style: existingProduct.productInfo.style,
          design: existingProduct.productInfo.design,
          amazon_skus: sku,
        });
        this.logger.debug(`NEW | ${sku} |  ${inserted.amazon_skus}`);
        await this.productRepository.save(inserted);
      }
    } catch (error) {
      this.handleError(error, sku);
    }
  }

  // Update Etsy product
  async updateEtsy(item: any): Promise<void> {
    try {
      item.product.etsy_offerings = item.etsy_offerings;
      await this.productRepository.save(item.product);
    } catch (error) {
      this.handleError(error, item.product);
    }
  }

  // Create a new product
  private createProduct(
    productData: any,
    qty: number,
    designTitle: string | undefined,
  ): ProductEntity {
    return this.productRepository.create({
      externalProductId: productData.asin || '-',
      externalCreatedAt: new Date(),
      updatedAt: new Date(),
      amazon_quantity: qty,
      source: 'Amazon',
      status: productData.condition,
      note: productData.sellerSku || '',
      sku: productData.sellerSku || '',
      design: designTitle,
      size: productData.size,
      color: productData.color,
      style: productData.style,
    });
  }

  // Create a new product design
  private createProductDesign(
    product: ProductEntity,
    title: string | undefined,
    qty: number,
  ): ProductDesignEntity {
    const newDesign = new ProductDesignEntity();
    newDesign.title = title;
    newDesign.quantity = qty;
    newDesign.product = product;
    this.productDesignRepository.save(newDesign);
    return newDesign;
  }

  // Get products by external ID
  async getProductsByExternalId(
    externalProductId: string,
  ): Promise<ProductDto[]> {
    const queryBuilder = this.productRepository.createQueryBuilder('product');
    const products = await queryBuilder
      .where('product.external_product_id = :externalProductId', {
        externalProductId,
      })
      .orderBy('product.style', 'ASC')
      .addOrderBy('product.size', 'ASC')
      .getMany();

    return products.map((product) => product.toDto());
  }

  // Get all products with pagination and filtering
  async getAllProducts(
    paginateOptions: PaginateProductsOptionsDto,
    data?: string,
  ): Promise<PageDto<ProductDto>> {
    const cacheKey = JSON.stringify(
      `getAllProducts${JSON.stringify(paginateOptions)}/${data}`,
    );
    const cachedData: any = await this.cacheService.get(cacheKey);
    if (cachedData) {
      this.logger.debug('cachedData');
      return cachedData;
    }

    const queryBuilder = this.productRepository.createQueryBuilder('product');
    queryBuilder.where(`product.status ilike 'active'`);
    if (data) {
      try {
        const jsonData = JSON.parse(data);
        const { query } = jsonData;
        if (query) {
          const keywords = query.split(' ');
          queryBuilder.andWhere(
            new Brackets((subQb) => {
              subQb.andWhere([
                { style: ILike(`%${keywords[0]}%`) },
                { sku: ILike(`%${keywords[0]}%`) },
                { color: ILike(`%${keywords[0]}%`) },
              ]);
              for (let i = 1; i < keywords.length; i++) {
                subQb.andWhere([
                  { style: ILike(`%${keywords[i]}%`) },
                  { sku: ILike(`%${keywords[i]}%`) },
                  { color: ILike(`%${keywords[i]}%`) },
                ]);
              }
            }),
          );
        }
      } catch (error) {
        console.error('Error parsing JSON:', error);
      }
    }

    const sortKey = paginateOptions.orderBy ?? 'externalCreatedAt';
    const sortOrder = paginateOptions.order;

    if (sortKey && sortOrder) {
      const isValidSortKey = this.productRepository.metadata.columns.some(
        (column) => column.propertyName === sortKey,
      );

      if (isValidSortKey) {
        queryBuilder.orderBy(
          `product.${sortKey}`,
          sortOrder.toUpperCase() as 'ASC' | 'DESC',
        );
      } else {
        // throw new Error(`Invalid sortKey: ${sortKey}`);
      }
    }

    const [items, pageMetaDto] = await queryBuilder.paginate(paginateOptions);
    const response = items.toPageDto<ProductDto>(pageMetaDto);

    await this.cacheService.set(cacheKey, response, 300);

    return response;
  }

  /**
   * Get products sorted by style, color, and size with pagination
   * @param paginateOptions - Pagination options
   * @param data - Additional query data
   * @returns Paginated and sorted products
   */
  async getSortedProducts(
    paginateOptions: PaginateProductsOptionsDto,
    data?: string,
  ): Promise<PageDto<ProductDto>> {
    const queryBuilder = this.productRepository.createQueryBuilder('product');

    queryBuilder.where(`product.inventory_management ilike 'shopify'`);
    queryBuilder.andWhere('product.inventory_item_id IS NOT NULL');
    queryBuilder.andWhere(`product.style IS NOT NULL and product.style != ''`);
    queryBuilder.andWhere(`product.color IS NOT NULL and product.color != ''`);
    if (data) {
      const jsonData = JSON.parse(data);
      const { query, showAll, sortBy, order } = jsonData;

      if (!showAll) {
        const startDate = subMonths(new Date(), 3);
        queryBuilder.andWhere('checking_time < :startDate', {
          startDate,
        });
      }

      if (query) {
        const keywords = query.split(' ');
        queryBuilder.andWhere(
          new Brackets((subQb) => {
            subQb.andWhere([
              { style: ILike(`%${keywords[0]}%`) },
              { color: ILike(`%${keywords[0]}%`) },
              {
                size: isNaN(parseInt(keywords[0]))
                  ? -1
                  : Equal(parseInt(keywords[0])),
              },
            ]);

            for (let i = 1; i < keywords.length; i++) {
              subQb.andWhere([
                { style: ILike(`%${keywords[i]}%`) },
                { color: ILike(`%${keywords[i]}%`) },
                {
                  size: isNaN(parseInt(keywords[i]))
                    ? -1
                    : Equal(parseInt(keywords[i])),
                },
              ]);
            }
          }),
        );
      }

      if (sortBy && order) {
        queryBuilder.orderBy(
          `product.${sortBy}`,
          order.toUpperCase() as 'ASC' | 'DESC',
        );
      }
    }

    const [items, pageMetaDto] = await queryBuilder.paginate(paginateOptions);
    return items.toPageDto<ProductDto>(pageMetaDto);
  }

  // Get a product by ID
  async getProduct(productId: string): Promise<ProductDto | undefined> {
    const queryBuilder = this.productRepository.createQueryBuilder('product');
    const product = await queryBuilder
      .where({
        id: productId,
      })
      .getOne();

    return product?.toDto();
  }

  // Set a note to a product
  async setNoteToProduct(
    productId: string,
    note: string,
  ): Promise<ProductDto | undefined> {
    const queryBuilder = this.productRepository.createQueryBuilder('product');
    const product = await queryBuilder
      .where({
        id: productId,
      })
      .getOne();

    if (product) {
      product.note = note;
      await this.productRepository.save(product);
    }

    return product?.toDto();
  }

  // Handle errors
  private async handleError(error: any, product: any): Promise<void> {
    this.logger.error(`updateAmazon error => ${error}`);
    this.logger.debug(`updateAmazon error => ${JSON.stringify(product)}`);
  }
}
