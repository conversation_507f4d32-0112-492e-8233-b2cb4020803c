/* eslint-disable @typescript-eslint/naming-convention */
import {
  HttpException,
  Injectable,
  Logger,
  OnModuleInit,
  OnModuleDestroy,
} from '@nestjs/common';
import { <PERSON>ron, CronExpression } from '@nestjs/schedule';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import moment from 'moment';

import { ApiConfigService } from '../../../common/services/api-config.service';
import { OrderEntity } from '../../../entities/order.entity';
import { CountryService } from '../../utils/country.util';
import { convertToSQLDateTimeWithoutTime } from '../../utils/date.util';
import { ProductService } from './product.service';
import Axios, { AxiosInstance } from 'axios';
import { ProductDto } from '../dto/product.dto';
import { Job, Queue } from 'bull';
import { Processor, InjectQueue, Process } from '@nestjs/bull';
import * as xlsx from 'xlsx';
import { ProductEntity } from '../../../entities/product.entity';

@Injectable()
@Processor('Amazon')
export class AmazonService implements OnModuleInit, OnModuleDestroy {
  private axiosHandler: AxiosInstance | null = null;
  private readonly MarketplaceIds = 'ATVPDKIKX0DER'; // Just US right now
  private readonly logger = new Logger(AmazonService.name);

  constructor(
    private readonly countryService: CountryService,
    private readonly productService: ProductService,
    private readonly appConfig: ApiConfigService,
    @InjectRepository(OrderEntity)
    private readonly orderRepository: Repository<OrderEntity>,
    @InjectQueue('Amazon') private _Q: Queue,
  ) {}

  async onModuleInit() {
    this.initializeAxiosHandler();
    await this.clearQueue();
    if (this.appConfig.isProduction) {
      await this.orderJob();
    }

    await this.orderJob();

    // await this.updateAllAmazonListings();
  }

  async onModuleDestroy() {
    try {
      // Clean up any pending jobs
      await this.clearQueue();

      // Close any open connections
      if (this.axiosHandler) {
        this.axiosHandler = null;
      }

      // Clear any cached data
      await this._Q.clean(0, 'completed');
      await this._Q.clean(0, 'active');
      await this._Q.clean(0, 'failed');
      await this._Q.empty();

      this.logger.log('AmazonService resources cleaned up successfully');
    } catch (error) {
      this.logger.error('Error cleaning up AmazonService resources:', error);
    }
  }

  /**
   * Initialize Axios Handler with Interceptors
   */
  private initializeAxiosHandler() {
    this.axiosHandler = Axios.create({
      baseURL: this.appConfig.amazonAPIBaseUrl,
      headers: {
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Headers': 'Authorization',
        'Content-Type': 'application/json',
      },
    });

    this.axiosHandler.interceptors.response.use(
      (response) => response,
      async (error) => this.handleAxiosError(error),
    );
  }

  /**
   * Handle Axios Errors
   *
   * @param error - The error response from Axios
   * @returns A promise rejection with the error
   */
  private async handleAxiosError(error: any) {
    const { config, response } = error;
    this.logRequestDetails(config.url, config.params);

    if (response?.status === 429) {
      this.logger.error('Too many requests, retrying...');
      await new Promise((resolve) => setTimeout(resolve, 5000));
      return this.axiosHandler?.request(config);
    } else if (response?.status === 403) {
      this.logger.error('403 Forbidden, refreshing token...');
      let retryCount = 0;
      const maxRetries = 3;

      while (retryCount < maxRetries) {
        try {
          await new Promise((resolve) => setTimeout(resolve, 2000));
          const accessToken = await this.getAccessToken();
          if (!accessToken) {
            throw new Error('Failed to obtain access token');
          }
          if (this.axiosHandler) {
            this.axiosHandler.defaults.headers.common['x-amz-access-token'] =
              accessToken;
          }
          return this.axiosHandler?.request(config);
        } catch (tokenError) {
          this.logger.error(`Failed to refresh token`);
          retryCount++;
          if (retryCount === maxRetries) {
            this.logger.error('Max retries reached for token refresh');
            break;
          }
        }
      }
    }

    this.logger.error(`${response?.status} | ${error?.message}`);
    return Promise.reject(error);
  }

  /**
   * Log Axios Request Details
   *
   * @param url - The request URL
   * @param params - The request parameters
   */
  private logRequestDetails(url: string, params: any) {
    this.logger.error(`Request URL: ${url}`);
    if (params) {
      this.logger.error('Request Params:', params);
    }
  }

  /**
   * Generate and Set Access Token
   */
  private async getAccessToken(): Promise<string | null> {
    this.logger.log('Fetching new access token...');
    const accessToken = await this.refreshToken();

    if (accessToken) {
      this.setAccessToken(accessToken);
      this.logger.log(`AccessToken: ${accessToken}`);
    }

    return accessToken;
  }

  /**
   * Refresh Amazon Access Token
   *
   * @returns The new access token or null if the refresh fails
   */
  private async refreshToken(): Promise<string | null> {
    try {
      const refreshTokenApi = Axios.create({
        baseURL: 'https://api.amazon.com/',
        headers: {
          'Access-Control-Allow-Origin': '*',
          'Access-Control-Allow-Headers': 'Authorization',
          'Content-Type': 'application/x-www-form-urlencoded;charset=UTF-8',
        },
      });

      const tokenResponse = await refreshTokenApi.post('auth/o2/token', {
        grant_type: 'refresh_token',
        client_id: this.appConfig.amazonClientId,
        refresh_token: this.appConfig.amazonRefreshToken,
        client_secret: this.appConfig.amazonClientSecret,
      });

      return tokenResponse.data.access_token;
    } catch (error) {
      this.logger.error(error);
      return null;
    }
  }

  /**
   * Set Access Token for Axios Handler
   *
   * @param accessToken - The new access token
   */
  private setAccessToken(accessToken: string): void {
    if (this.axiosHandler) {
      this.axiosHandler.defaults.headers.common['x-amz-access-token'] =
        accessToken;
    }
  }

  /**
   * Clear Bull Queue
   */
  async clearQueue(): Promise<void> {
    try {
      await this._Q.clean(0, 'completed');
      await this._Q.clean(0, 'active');
      await this._Q.clean(0, 'failed');
      await this._Q.empty();
    } catch (error) {
      this.logger.error('Error clearing the queue:', error);
    }
  }

  /**
   * Add Job to Bull Queue
   *
   * @param type - The type of the job
   * @param params - The job parameters
   * @param nextPage - The next page token (optional)
   */
  async addJobToQueue(
    type: string,
    params: any,
    nextPage?: any,
  ): Promise<void> {
    try {
      const job = await this._Q.add(
        { type, params, nextPage },
        { delay: 2000 },
      );
      this.logger.log(`Job added: ${job.id}, Type: ${type}`);
    } catch (error) {
      this.logger.error(error);
    }
  }

  /**
   * Process Bull Queue Jobs
   *
   * @param job - The job to process
   */
  @Process()
  async consumeQ(job: Job<any>) {
    this.logger.debug(`Processing Amazon job: ${job.data.type}`);
    await new Promise((resolve) => setTimeout(resolve, 2000));

    switch (job.data.type) {
      case 'order':
        await this.fetchOrders(job.data.params, job.data.nextPage);
        break;
      case 'itemDetail':
        await this.getOrderItems(job.data.order);
        break;
      case 'inventory':
        await this.fetchInventory(job.data.params, job.data.nextPage);
        break;
      default:
        this.logger.error(`Unknown job type: ${job.data.type}`);
    }
  }

  /**
   * Scheduled Cron Jobs
   */
  // Order job runs every 2 hours between 6 AM and 11 PM
  // @Cron('0 */2 * * *')
  async orderJob() {
    this.logger.debug('Starting order job');
    await this.getAccessToken();

    const lastUpdatedAfter = new Date();
    lastUpdatedAfter.setHours(lastUpdatedAfter.getHours() - 3);

    const params = {
      MarketplaceIds: this.MarketplaceIds,
      LastUpdatedAfter: '2025-07-01T00:00:00Z',
      MaxResultsPerPage: 100,
    };

    this.logger.warn(params);
    await this.addJobToQueue('order', params);
  }

  // Fetch inventory every day at 11 AM
  @Cron(CronExpression.EVERY_DAY_AT_11AM)
  async inventoryJob() {
    this.logger.debug('Starting inventory job');

    const startDateTime = moment().subtract(16, 'months').toISOString();

    const params = {
      marketplaceIds: 'ATVPDKIKX0DER',
      granularityType: 'Marketplace',
      granularityId: 'ATVPDKIKX0DER',
      details: true,
      startDateTime,
    };

    await this.getAccessToken();
    // await this.productService.updateAllAmazonQuantitiesToZero();
    await this.addJobToQueue('inventory', params);
  }

  /**
   * Check the status of a submitted feed.
   *
   * @param feedId - The ID of the feed to check.
   * @returns The processing status of the feed.
   */
  async checkFeedStatus(feedId: string): Promise<string> {
    try {
      // Step 1: Make a request to get the feed status
      const response = await this.axiosHandler?.get(
        `feeds/2021-06-30/feeds/${feedId}`,
      );

      // Step 2: Extract the processing status from the response
      const feedProcessingStatus = response?.data.processingStatus;
      this.logger.log(
        `Feed ID ${feedId} processing status: ${feedProcessingStatus}`,
      );

      // Step 3: Return the processing status
      return feedProcessingStatus;
    } catch (error: any) {
      this.logger.error(`Error checking feed status: ${error.message}`);
      throw error;
    }
  }

  // Update all Amazon listings every day at 10:15 AM
  @Cron('15 10 * * *')
  async updateAllAmazonListings(): Promise<void> {
    await this.getAccessToken();
    try {
      const products = await this.productService.getAmazonProducts();
      this.logger.debug(`Found ${products.length} products with Amazon SKUs.`);
      if (products.length === 0) {
        this.logger.log('No products found with non-empty amazon_skus.');
        return;
      }
      const batchSize = 200;
      for (let i = 0; i < products.length; i += batchSize) {
        const productBatch = products.slice(i, i + batchSize);
        await this.updateAmazonListingsInBatches(productBatch);
        await new Promise((resolve) => setTimeout(resolve, 5000));
      }
    } catch (error: any) {
      this.logger.error(`Error updating Amazon listings: ${error.message}`);
    }
  }

  /**
   * Update Amazon listings in batches
   *
   * @param products - Array of products to update
   */
  async updateAmazonListingsInBatches(
    products: ProductEntity[],
  ): Promise<void> {
    try {
      await new Promise((resolve) => setTimeout(resolve, 5000));

      // Step 1: Create a feed document
      const feedDocumentResponse = await this.axiosHandler?.post(
        'feeds/2021-06-30/documents',
        {
          contentType: 'application/xml',
        },
      );

      const { feedDocumentId, url } = feedDocumentResponse?.data ?? {};

      // Step 2: Prepare the XML data for updating the quantities
      const messages = products
        .flatMap((product, productIndex) => {
          const skus = product.amazon_skus?.split(',') ?? [];
          return skus.map(
            (sku, index) => `
          <Message>
            <MessageID>${productIndex * 100 + index + 1}</MessageID>
            <OperationType>Update</OperationType>
            <Inventory>
              <SKU>${sku.trim()}</SKU>
              <Quantity>${(product.shopify_quantity ?? 0 > 0) ? product.shopify_quantity : 0}</Quantity>
            </Inventory>
          </Message>
        `,
          );
        })
        .join('');

      const xmlData = `<?xml version="1.0" encoding="UTF-8"?>
      <AmazonEnvelope xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="amzn-envelope.xsd">
        <Header>
          <DocumentVersion>1.01</DocumentVersion>
          <MerchantIdentifier>A22JT016Q69J6P</MerchantIdentifier>
        </Header>
        <MessageType>Inventory</MessageType>
        ${messages}
      </AmazonEnvelope>`;

      // Step 3: Upload the XML data to Amazon using the pre-signed URL
      await Axios.put(url, xmlData, {
        headers: {
          'Content-Type': 'application/xml',
        },
      });

      // Step 4: Submit the feed
      const submitFeedResponse = await this.axiosHandler?.post(
        'feeds/2021-06-30/feeds',
        {
          feedType: 'POST_INVENTORY_AVAILABILITY_DATA',
          marketplaceIds: [this.MarketplaceIds],
          inputFeedDocumentId: feedDocumentId,
        },
      );

      const feedId = submitFeedResponse?.data.feedId;
      this.logger.log(`Feed submitted successfully. Feed ID: ${feedId}`);

      // Step 5: Update the last updated date for each product
      for (const product of products) {
        if (product.externalProductId) {
          await this.productService.updateAmazonDate(product.externalProductId);
        }
      }
    } catch (error: any) {
      this.logger.error(
        `Error updating Amazon listing quantities in batches: ${error.message}`,
      );
    }
  }

  /**
   * Update the quantities of multiple SKUs in Amazon.
   *
   * @param product - The product entity containing the SKUs to update.
   * @param quantity - The new quantity to set for each SKU.
   */
  async updateListingQuantities(products: ProductDto[]): Promise<void> {
    try {
      await this.getAccessToken();
      for (const product of products) {
        await new Promise((resolve) => setTimeout(resolve, 1000));
        // Extract and split the SKUs
        const skus = product.amazon_skus?.split(',') ?? [];

        // Step 1: Create a feed document
        const feedDocumentResponse = await this.axiosHandler?.post(
          'feeds/2021-06-30/documents',
          {
            contentType: 'application/xml',
          },
        );

        const { feedDocumentId, url } = feedDocumentResponse?.data ?? {};

        // Step 2: Prepare the XML data for updating the quantities
        const messages = skus
          .map(
            (sku, index) => `
        <Message>
          <MessageID>${index + 1}</MessageID>
          <OperationType>Update</OperationType>
          <Inventory>
            <SKU>${sku.trim()}</SKU>
            <Quantity>${(product.shopify_quantity ?? 0) > 0 ? product.shopify_quantity : 0}</Quantity>
          </Inventory>
        </Message>
      `,
          )
          .join('');

        const xmlData = `<?xml version="1.0" encoding="UTF-8"?>
      <AmazonEnvelope xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="amzn-envelope.xsd">
        <Header>
          <DocumentVersion>1.01</DocumentVersion>
          <MerchantIdentifier>A22JT016Q69J6P</MerchantIdentifier>
        </Header>
        <MessageType>Inventory</MessageType>
        ${messages}
      </AmazonEnvelope>`;

        // Step 3: Upload the XML data to Amazon using the pre-signed URL
        await Axios.put(url, xmlData, {
          headers: {
            'Content-Type': 'application/xml',
          },
        });
        await new Promise((resolve) => setTimeout(resolve, 100));

        // Step 4: Submit the feed
        const submitFeedResponse = await this.axiosHandler?.post(
          'feeds/2021-06-30/feeds',
          {
            feedType: 'POST_INVENTORY_AVAILABILITY_DATA',
            marketplaceIds: [this.MarketplaceIds],
            inputFeedDocumentId: feedDocumentId,
          },
        );

        const feedId = submitFeedResponse?.data.feedId;
        if (product.externalProductId) {
          await this.productService.updateAmazonDate(product.externalProductId);
        }
        this.logger.log(`Feed submitted successfully. Feed ID: ${feedId}`);
        await new Promise((resolve) => setTimeout(resolve, 1500));
      }
    } catch (error: any) {
      this.logger.error(`Error updating listing quantities: ${error.message}`);
    }
  }

  /**
   * Fetch Orders from Amazon
   *
   * @param params - The parameters for fetching orders
   * @param nextPage - The next page token (optional)
   */
  async fetchOrders(params: any, nextPage?: string): Promise<void> {
    try {
      const result = await this.axiosHandler?.get('orders/v0/orders', {
        params,
      });
      nextPage = this.parseNextPageLink(result?.data.payload);

      this.logger.log(
        `Found ${result?.data.payload.Orders.length} Amazon orders`,
      );

      for (const order of result?.data.payload.Orders ?? []) {
        setTimeout(async () => {
          await this._Q.add({ type: 'itemDetail', order });
          this.logger.debug(
            `Added itemDetail job for order: ${order.AmazonOrderId}`,
          );
        }, 1000);
      }

      const fetchedPages = new Set<string>();
      if (nextPage && !fetchedPages.has(nextPage)) {
        params.NextToken = nextPage;
        fetchedPages.add(nextPage);
        setTimeout(async () => {
          await this.addJobToQueue('order', params, nextPage);
        }, 1000);
      } else {
        this.logger.log('Finished fetching orders');
      }
    } catch (error: any) {
      this.logger.error(`Error fetching orders: ${error.message}`);
    }
  }

  /**
   * Fetch Inventory from Amazon
   *
   * @param params - The parameters for fetching inventory
   * @param nextPage - The next page token (optional)
   */
  async fetchInventory(params: any, nextPage?: string): Promise<void> {
    try {
      const response = await this.axiosHandler?.get(
        'fba/inventory/v1/summaries',
        { params },
      );
      nextPage = response?.data?.pagination?.nextToken;

      for (const item of response?.data?.payload?.inventorySummaries ?? []) {
        this.productService.updateAmazon(item);
      }

      const fetchedPages = new Set<string>();
      if (nextPage && !fetchedPages.has(nextPage)) {
        params.nextToken = nextPage;
        fetchedPages.add(nextPage);
        setTimeout(async () => {
          await this.addJobToQueue('inventory', params, nextPage);
        }, 1000);
      } else {
        this.logger.log('Finished fetching inventory');
      }
    } catch (error: any) {
      this.logger.error(`Error fetching inventory: ${error.message}`);
    }
  }

  /**
   * Fetch Order Items
   *
   * @param order - The order for which items are to be fetched
   */
  async getOrderItems(order: any): Promise<void> {
    try {
      await new Promise((resolve) => setTimeout(resolve, 1000));
      await this.processOrder(order.AmazonOrderId, order);
    } catch (error: any) {
      this.logger.error(`Error fetching order items: ${error.message}`);
    }
  }

  /**
   * Process Order and Save Details
   *
   * @param orderId - The Amazon Order ID
   * @param orderData - The order data (optional)
   */
  private async processOrder(orderId: string, orderData?: any): Promise<void> {
    const [orderDetails, order] = await Promise.all([
      this.getOrderItemsFromApi(orderId),
      orderData
        ? Promise.resolve(orderData)
        : this.getOrderDataFromApi(orderId),
    ]);

    await this.getOrderDetail(order, orderDetails);
  }

  /**
   * Fetch Order Items from Amazon API
   *
   * @param orderId - The Amazon Order ID
   * @returns The order items
   */
  private async getOrderItemsFromApi(orderId: string): Promise<any> {
    this.logger.log(`Fetching order items for order: ${orderId}`);
    this.logger.log(
      `Headers: ${this.axiosHandler?.defaults.headers.common['x-amz-access-token']}`,
    );
    const orderResult = await this.axiosHandler?.get(
      `orders/v0/orders/${orderId}/orderItems`,
    );
    return orderResult?.data.payload;
  }

  /**
   * Fetch Order Data from Amazon API
   *
   * @param orderId - The Amazon Order ID
   * @returns The order data
   */
  private async getOrderDataFromApi(orderId: string): Promise<any> {
    const orderResult = await this.axiosHandler?.get(
      `orders/v0/orders/${orderId}`,
    );
    return orderResult?.data.payload;
  }

  /**
   * Parse Next Page Link from Amazon Response
   *
   * @param data - The Amazon response data
   * @returns The next page token
   */
  private parseNextPageLink(data: any | null): string {
    return data?.NextToken || '';
  }

  /**
   * Fetch and Delete Existing Order
   *
   * @param orderId - The Amazon Order ID
   * @returns Whether the order was saved previously
   */
  private async fetchAndDeleteOrder(
    orderId: string,
  ): Promise<boolean | undefined> {
    const existingOrder = await this.orderRepository
      .createQueryBuilder('order')
      .select('order.saved')
      .where('external_order_id = :id', { id: `AMZ${orderId}` })
      .getOne();

    if (existingOrder) {
      await this.orderRepository
        .createQueryBuilder()
        .delete()
        .from(OrderEntity)
        .where('external_order_id = :id', { id: `AMZ${orderId}` })
        .execute();
      this.logger.warn(`Deleted existing order: AMZ${orderId}`);
      return existingOrder.saved;
    }
    return false;
  }

  /**
   * Get Order Details and Save to Database
   *
   * @param order - The order data
   * @param orderDetails - The order details
   */
  async getOrderDetail(order: any, orderDetails: any): Promise<void> {
    const savedValue = await this.fetchAndDeleteOrder(
      orderDetails.AmazonOrderId,
    );

    try {
      const orderLength = orderDetails.OrderItems.length;
      const customer_name = 'Amazon Customer';

      const insertPromises = orderDetails.OrderItems.map(
        async (item: any, index: number) => {
          const product = await this.productService.findProduct({
            sku: item.SellerSKU,
          });
          const { color, size, style, design } = product.productInfo;

          let amount = 0;
          let tax = 0;
          let shippingPrice = 0;
          let totalAmount = 0;
          let currencyCode = '?';

          try {
            amount = Number.parseFloat(`${item.ItemPrice.Amount}`);
          } catch (_) {}
          try {
            totalAmount = Number.parseFloat(`${order.OrderTotal.Amount}`);
          } catch (_) {}
          try {
            tax = Number.parseFloat(`${item.ItemTax.Amount}`);
          } catch (_) {}
          try {
            shippingPrice = Number.parseFloat(`${item.ShippingPrice.Amount}`);
          } catch (_) {}

          try {
            currencyCode = `${item.ItemPrice.CurrencyCode}`;
          } catch (_) {}

          await this.productService.sellItem(item.SellerSKU);

          const receivedDate = new Date(order.PurchaseDate);
          const adjustedDate = convertToSQLDateTimeWithoutTime(receivedDate);

          const inserted = this.orderRepository.create({
            externalCreatedAt: this.appConfig.isDevelopment
              ? order.PurchaseDate
              : adjustedDate,
            externalOrderId: `AMZ${orderDetails.AmazonOrderId}`,
            externalPrice: amount,
            externalCurrency: currencyCode,
            current_subtotal_price: index === 0 ? totalAmount : 0,
            total_shipping_price: index === 0 ? shippingPrice : 0,
            total_discounts: index === 0 ? 0 : 0,
            total_tax: index === 0 ? tax : 0,
            price: amount,
            saved: savedValue,
            status: order.OrderStatus,
            city: order.ShippingAddress?.City,
            country: this.countryService.getCountryNameByCode(
              order.ShippingAddress?.CountryCode,
            ),
            province: this.countryService.getStateName(
              this.countryService.getCountryNameByCode(
                order.ShippingAddress?.CountryCode,
              ),
              order.ShippingAddress?.StateOrRegion,
            ),
            order_number: 0,
            email: order.BuyerInfo.BuyerEmail ?? '<EMAIL>',
            fulfillmentChannel: order.FulfillmentChannel,
            salesChannel: order.SalesChannel,
            customer_name,
            order_id: String(item.OrderItemId),
            item_no: orderLength === 1 ? '' : `${index + 1}/${orderLength}`,
            message: '',
            data: !color || !style || size < 1 ? JSON.stringify(order) : '',
            style,
            outside: design ?? '-',
            size,
            color,
            inside: '',
            goldInlay: '',
            quantity: Number.parseInt(item.QuantityOrdered),
            sku: item.SellerSKU,
            source: 'Amazon',
          });

          await this.orderRepository.save(inserted);

          this.logger.debug(
            `Inserted order item: AMZ${orderDetails.AmazonOrderId} | ${inserted.externalCreatedAt}`,
          );
        },
      );

      await Promise.all(insertPromises);
    } catch (error: any) {
      this.logger.error(`Error fetching order details: ${error.message}`);
    }
  }

  // Upload a file and process it
  async updateInventoryFromExcel(file: Express.Multer.File): Promise<boolean> {
    try {
      const workbook = xlsx.read(file.buffer, { type: 'buffer' });
      const sheets = Object.keys(workbook.Sheets);

      for (let index = 0; index < sheets.length; index++) {
        const sheetName = sheets[index];
        const worksheet = workbook.Sheets[sheetName];
        const data = xlsx.utils.sheet_to_json(worksheet);

        this.logger.warn(
          `Processing sheet: ${sheetName} | ${data.length} rows`,
        );

        const skuList = data.map((row: any) => ({
          fulfillmentChannel: row['fulfillment-channel'],
          sellerSku: row['seller-sku'],
        }));

        for (const { fulfillmentChannel, sellerSku } of skuList) {
          if (sellerSku && fulfillmentChannel === 'DEFAULT') {
            await this.productService.updateAmazonSKUs(sellerSku);
          }
        }
      }

      return true;
    } catch (error) {
      console.error('Error processing XLSX file:', error);
      throw new HttpException('Error processing XLSX file', 500);
    }
  }
}
