import {
  Injectable,
  Logger,
  HttpException,
  OnModuleInit,
  OnModuleDestroy,
} from '@nestjs/common';
import { Cron, CronExpression } from '@nestjs/schedule';
import { InjectRepository } from '@nestjs/typeorm';
import Axios, { AxiosInstance } from 'axios';
import { Job, Queue } from 'bull';
import { Repository } from 'typeorm';
import { InjectQueue, Process, Processor } from '@nestjs/bull';
import { subDays, subHours } from 'date-fns';
import * as xlsx from 'xlsx';

import { ApiConfigService } from '../../../common/services/api-config.service';
import { parseShopifyOrders } from '../../utils/shopify-parser.util';
import { OrderEntity } from '../../../entities/order.entity';
import { CountryService } from '../../utils/country.util';
import { ProductEntity } from '../../../entities/product.entity';
import { ProductDto } from '../dto/product.dto';
import { convertToSQLDateTimeWithoutTime } from '../../utils/date.util';
import { ProductService } from './product.service';
import { Order } from '../../../common/constants';

@Injectable()
@Processor('Shopify')
export class ShopifyService implements OnModuleInit, OnModuleDestroy {
  private axiosHandler: AxiosInstance | null = null;
  private readonly logger = new Logger(ShopifyService.name);

  constructor(
    private readonly countryService: CountryService,
    private readonly productService: ProductService,
    private readonly appConfig: ApiConfigService,
    @InjectRepository(ProductEntity)
    private readonly productRepository: Repository<ProductEntity>,
    @InjectRepository(OrderEntity)
    private readonly orderRepository: Repository<OrderEntity>,
    @InjectQueue('Shopify') private _Q: Queue,
  ) {
    this.axiosHandler = Axios.create({
      baseURL: appConfig.shopifyAPIBaseUrl,
      headers: {
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Headers': 'Authorization',
        'X-Shopify-Access-Token': appConfig.shopifyToken,
      },
    });

    this.axiosHandler.interceptors.response.use(
      (response) => response,
      async (error) => {
        if (error.response && error.response.status === 429) {
          this.logger.error('Too many requests, retrying...');
          await new Promise((resolve) => setTimeout(resolve, 5000));
          return this.axiosHandler?.request(error.config);
        }
        return Promise.reject(error);
      },
    );
  }

  async onModuleInit() {
    // await this.addNewAmazonSKU();
    await this.clearQueue();
    if (this.appConfig.isProduction) {
      await this.orderJob();
    }
  }

  async onModuleDestroy() {
    try {
      // Clean up any pending jobs
      await this.clearQueue();

      // Close any open connections
      if (this.axiosHandler) {
        this.axiosHandler = null;
      }

      // Clear any cached data
      await this._Q.clean(0, 'completed');
      await this._Q.clean(0, 'active');
      await this._Q.clean(0, 'failed');
      await this._Q.empty();

      this.logger.log('ShopifyService resources cleaned up successfully');
    } catch (error) {
      this.logger.error('Error cleaning up ShopifyService resources:', error);
    }
  }

  /**
   * Cron Jobs - Scheduled Functions
   */

  // Order job runs every 2 hours between 6 AM and 11 PM
  @Cron('0 */2 * * *')
  async orderJob() {
    this.logger.debug('job');
    let date = subHours(new Date(), 3);

    const params = {
      updated_at_min: date.toISOString(),
      status: 'any',
    };

    try {
      const job = await this._Q.add(
        {
          type: 'order',
          params,
        },
        {
          delay: 50,
        },
      );
      this.logger.log(`job: ${JSON.stringify(job.id)}`);
    } catch (error) {
      this.logger.error(`job: ${error}`);
    }
  }

  // Product job runs every day at 9:15 AM
  @Cron('15 9 * * *')
  async productJob() {
    this.logger.warn('productJob');
    await this.productService.deleteUnusedItems();
    await this.productService.setAllProductsOosDate();
    const params = {
      updated_at_min: subDays(new Date(), 1).toISOString(),
      status: 'active',
    };
    const job = await this._Q.add({
      type: 'product',
      params,
    });
    this.logger.debug(`job ${job.id}`);
  }

  // Product job runs every day at 9:15 AM
  @Cron('15 11 * * *')
  async deleteArchivedProductsJob() {
    this.logger.warn('deleteArchivedProducts');
    const params = {
      status: 'archived',
    };
    const job = await this._Q.add({
      type: 'deleteArchived',
      params,
    });
    this.logger.debug(`job ${job.id}`);
  }

  // Product job runs every day at 11:20 AM
  async deleteHiddenProducts() {
    this.logger.warn('deleteHiddenProducts');
    const params = {
      product_type: 'hidden',
    };
    const job = await this._Q.add({
      type: 'deleteArchived',
      params,
    });
    this.logger.debug(`job ${job.id}`);
  }

  @Cron(CronExpression.EVERY_DAY_AT_9AM)
  async autoDeductionJob() {
    let date = subDays(new Date(), 1);

    const items = await this.productService.getSoldProducts(
      {
        order: Order.DESC,
        page: 1,
        take: 0,
        skip: 0,
      },
      JSON.stringify({ date: date.toISOString() }),
      true,
    );

    this.logger.warn(`${items.data.length} items for autoDeduction`);
    items.data.forEach(async (e) => {
      const params = {
        inventoryId: e['s_inventory_item_id'],
        qty: -e['s_total_sales'],
        id: e['s_id'],
      };
      try {
        const job = await this._Q.add(
          {
            type: 'autoDeduction',
            params,
          },
          {
            delay: 100,
            removeOnComplete: true,
            removeOnFail: 3,
            backoff: {
              type: 'exponential',
              delay: 500,
            },
          },
        );
        this.logger.log(`job: ${JSON.stringify(job.id)}`);
      } catch (error) {
        this.logger.error(`job: ${error}`);
      }
    });
  }

  async autoDeductionProcess(params: any) {
    const res = await this.deductItemFromShopify(
      params.inventoryId,
      params.qty,
    );
    if (res != undefined) {
      this.productService.markAsSaved(params.id);
    }
  }

  /**
   * Job Processors
   */
  @Process()
  async consumeQ(job: Job<any>) {
    this.logger.debug(`Shopify Job ${job.data.type}`);

    if (job.data.type === 'order') {
      await this.fetchOrder(job.data.params, job.data.nextPage);
    }
    if (job.data.type === 'product') {
      await this.fetchProducts(job.data.params, job.data.nextPage);
    }
    if (job.data.type === 'deleteArchived') {
      await this.deleteArchivedItems(job.data.params, job.data.nextPage);
    }
    if (job.data.type === 'autoDeduction') {
      await this.autoDeductionProcess(job.data.params);
    }
  }

  /**
   * Helper Functions
   */

  // Clear the queue
  async clearQueue(): Promise<void> {
    try {
      await this._Q.clean(0, 'completed');
      await this._Q.clean(0, 'active');
      await this._Q.clean(0, 'failed');
      await this._Q.empty();
    } catch (error) {
      console.error('Error clearing the queue:', error);
    }
  }

  // Fetch orders from Shopify
  async fetchOrder(params: any, page: string) {
    const url = page ? `orders.json?page_info=${page}` : 'orders.json';
    this.logger.warn(url);
    this.logger.warn(params);
    try {
      const result = await this.axiosHandler?.get(url, {
        ...(page ? {} : { params }),
      });
      const _nextPage = this.parseNextPageLink(result?.headers.link);

      for (const order of result?.data.orders ?? []) {
        await this.checkAndPersistOrders(order).catch((error) =>
          this.logger.error(error.message),
        );
      }

      if (_nextPage && _nextPage !== page) {
        try {
          const job = await this._Q.add(
            {
              type: 'order',
              params,
              nextPage: _nextPage,
            },
            {
              delay: 100,
              removeOnComplete: true,
              removeOnFail: 3,
              backoff: {
                type: 'exponential',
                delay: 500,
              },
            },
          );
          this.logger.log(`job: ${JSON.stringify(job.id)}`);
        } catch (error) {
          this.logger.error(`job: ${error}`);
        }
      } else {
        this.logger.log('finish');
      }
    } catch (error) {
      this.logger.error(error);
    }
  }

  // Fetch products from Shopify
  async fetchProducts(params: any, page: string) {
    try {
      const url = page ? `products.json?page_info=${page}` : 'products.json';

      if (page) {
        delete params.updated_at_min;
        delete params.status;
      }

      const result = await this.axiosHandler?.get(url, { params });
      const _nextPage = this.parseNextPageLink(result?.headers.link);

      for (const product of result?.data.products ?? []) {
        for (const item of product.variants) {
          if (item.sku) {
            await this.productService.updateShopify(product, item);
          }
        }
      }

      if (_nextPage && _nextPage != page) {
        await this._Q.add({
          type: 'product',
          params,
          nextPage: _nextPage,
        });
      } else {
        this.logger.debug('Finish products shopify');
        // await this.deleteHiddenProducts();
      }
    } catch (e) {
      this.logger.error(e);
    }
  }

  // Fetch products from Shopify
  async deleteArchivedItems(params: any, page: string) {
    try {
      const url = page ? `products.json?page_info=${page}` : 'products.json';

      if (page) {
        delete params.updated_at_min;
        delete params.status;
        delete params.product_type;
      }

      const result = await this.axiosHandler?.get(url, { params });
      const _nextPage = this.parseNextPageLink(result?.headers.link);

      for (const product of result?.data.products ?? []) {
        this.logger.warn(product.title);
        for (const item of product.variants) {
          this.logger.verbose(item.sku);
          this.logger.debug(item.inventory_item_id);
          if (item.sku) {
            await this.productService.updateShopify(product, item);
          }
        }
      }

      if (_nextPage && _nextPage != page) {
        await this._Q.add({
          type: 'deleteArchived',
          params,
          nextPage: _nextPage,
        });
      } else {
        this.logger.debug('Finish delete archived shopify');
      }
    } catch (e) {
      this.logger.error(e);
    }
  }

  // Parse the next page link from the Shopify response
  private parseNextPageLink(linkHeader: any | null): string | null {
    if (!linkHeader) {
      return null;
    }
    const links = linkHeader.split(',');
    const nextLinks = links.find((l) => l.includes('rel="next"'));
    if (!nextLinks) {
      return null;
    }
    const match = nextLinks.match(/<([^>]*)>/);
    if (!match || match.length < 2) {
      return null;
    }
    const url = match[1];
    const urlParsed = new URL(url);
    return urlParsed.searchParams.get('page_info');
  }

  // Check and persist orders in the database
  private async checkAndPersistOrders(order: any): Promise<boolean> {
    const orderId = order.name;

    const prev = await this.orderRepository
      .createQueryBuilder('order')
      .where('order.order_id = :id', { id: orderId })
      .getOne();

    if (prev) {
      await this.orderRepository
        .createQueryBuilder()
        .delete()
        .from(OrderEntity)
        .where('order_id = :id', { id: orderId })
        .execute();
    }

    const orderLength = order.line_items.length;
    const customer = order.customer;
    const shipping = order.shipping_address;
    const customer_name = `${customer.first_name} ${customer.last_name}`;
    const insertPromises = order.line_items.map(async (item, index) => {
      const itemSpecification = await parseShopifyOrders(
        item,
        this.productService,
      );
      await this.productService.sellItem(item.sku);

      let city;
      let country;
      let province;
      let total_shipping_price;
      let current_total_price;

      try {
        city = shipping.city;
      } catch (e) {
        city = '';
      }

      try {
        country = shipping.country;
      } catch (e) {
        country = '';
      }

      try {
        province = shipping.province;
      } catch (e) {
        province = '';
      }

      try {
        total_shipping_price =
          order.total_shipping_price_set?.shop_money?.amount;
      } catch (e) {
        total_shipping_price = '?';
      }

      try {
        current_total_price = order.current_total_price;
      } catch (e) {
        current_total_price = '?';
      }

      try {
        const receivedDate = new Date(order.created_at);
        const adjustedDate = convertToSQLDateTimeWithoutTime(receivedDate);

        const total_amount =
          parseFloat(current_total_price) +
          parseFloat(total_shipping_price) -
          parseFloat(order.total_discounts);

        const inserted = this.orderRepository.create({
          externalCreatedAt: this.appConfig.isDevelopment
            ? order.created_at
            : adjustedDate,
          externalOrderId: String(order.id),
          externalPrice: order.total_price,
          externalCurrency: order.currency,
          current_subtotal_price: index == 0 ? total_amount : 0,
          total_shipping_price: index == 0 ? total_shipping_price : 0,
          total_discounts: index == 0 ? order.total_discounts : 0,
          total_tax: index == 0 ? order.total_tax : 0,
          price: itemSpecification.price,
          order_number: order.order_number,
          email: customer.email ?? '-',
          city: city,
          country: this.countryService.getCountryNameByCode(country ?? ''),
          province: this.countryService.getStateName(
            this.countryService.getCountryNameByCode(country ?? ''),
            province ?? '',
          ),
          status:
            order.fulfillment_status == null
              ? order.financial_status
              : order.fulfillment_status,
          customer_name,
          order_id: order.name,
          saved: prev?.saved ?? false,
          item_no: orderLength === 1 ? '' : `${index + 1}/${orderLength}`,
          message: order.note ?? '',
          style: itemSpecification.style,
          data:
            !itemSpecification.color ||
            !itemSpecification.style ||
            itemSpecification.size < 1
              ? JSON.stringify(order)
              : '',
          outside: itemSpecification.outsideEngraving,
          inside: itemSpecification.insideEngraving,
          goldInlay: itemSpecification.goldInlay,
          size: itemSpecification.size,
          quantity: itemSpecification.quantity,
          color: itemSpecification.color,
          sku: item.sku,
          source: 'Shopify',
        });

        await this.orderRepository.save(inserted);
        this.logger.debug(`order inserted => ${inserted.externalCreatedAt}`);
      } catch (e) {
        this.logger.error(`detail: ${JSON.stringify(itemSpecification)}`);
        this.logger.error(`order error ${e}`);
      }
    });

    await Promise.all(insertPromises);
    return true;
  }

  /**
   * Other Functions
   */

  // Update a single product's inventory in Shopify
  async deductItemFromShopify(
    inventory_item_id: string,
    qty: number,
  ): Promise<ProductDto | undefined> {
    const product = await this.productRepository.findOne({
      where: {
        inventory_item_id: inventory_item_id,
        inventory_management: 'shopify',
      },
    });

    if (!product) {
      throw new HttpException('Product not found', 404);
    }

    try {
      this.logger.warn(`Deducting ${qty} from ${inventory_item_id}`);

      const adjustmentQty = Math.abs(qty);

      await this.axiosHandler?.post('inventory_levels/adjust.json', {
        inventory_item_id: inventory_item_id,
        location_id: 4269408304,
        available_adjustment: -adjustmentQty,
      });

      product.shopify_quantity =
        (product.shopify_quantity ?? 0) - adjustmentQty;
      await this.productRepository.save(product);

      return product.toDto();
    } catch (error) {
      this.logger.error('Error updating Shopify inventory:', error);
      throw new HttpException('Shopify update failed', 500);
    }
  }

  // Update multiple products' inventory in Shopify
  async updateProducts(
    items: { inventory_item_id: string; qty: number }[],
  ): Promise<ProductDto[]> {
    const updatedProducts: ProductDto[] = [];

    for (const item of items) {
      try {
        await this.axiosHandler?.post('inventory_levels/set.json', {
          inventory_item_id: item.inventory_item_id,
          location_id: 4269408304,
          available: item.qty,
        });

        const product = await this.productRepository.findOne({
          where: {
            inventory_item_id: item.inventory_item_id,
            inventory_management: 'shopify',
          },
        });

        if (product) {
          product.inventory_item_id = item.inventory_item_id;
          product.shopify_quantity = item.qty;
          product.checkingTime = new Date();
          await this.productRepository.save(product);
          updatedProducts.push(product.toDto());
        } else {
          this.logger.warn(`Product not found: ${item.inventory_item_id}`);
        }
      } catch (error) {
        this.logger.error(
          `Error updating Shopify inventory for item ${item.inventory_item_id}:`,
          error,
        );
        throw new HttpException(
          `Shopify update failed for item ${item.inventory_item_id}`,
          500,
        );
      }
    }

    return updatedProducts;
  }
  // Update multiple products' inventory in Shopify
  async updateSingleProduct(item: {
    inventory_item_id: string;
    qty: number;
  }): Promise<void> {
    try {
      const product = await this.productRepository.findOne({
        where: {
          inventory_item_id: item.inventory_item_id,
          inventory_management: 'shopify',
        },
      });

      if (product) {
        await this.axiosHandler?.post('inventory_levels/set.json', {
          inventory_item_id: item.inventory_item_id,
          location_id: 4269408304,
          available: item.qty,
        });
        product.inventory_item_id = item.inventory_item_id;
        product.shopify_quantity = item.qty;
        product.checkingTime = new Date();
        await this.productRepository.save(product);
      } else {
        this.logger.warn(`Product not found: ${item.inventory_item_id}`);
      }
    } catch (error) {
      throw new HttpException(
        `Shopify update failed for item ${item.inventory_item_id}`,
        500,
      );
    }
  }

  // Upload a file and process it
  async uploadFile(file: Express.Multer.File): Promise<boolean> {
    try {
      const workbook = xlsx.read(file.buffer, { type: 'buffer' });
      const sheets = Object.keys(workbook.Sheets);

      for (let index = 0; index < sheets.length; index++) {
        const sheetName = sheets[index];
        const worksheet = workbook.Sheets[sheetName];
        const data = xlsx.utils.sheet_to_json(worksheet);
        if (sheetName != 'D6') {
          continue;
        }
        const skuList = data.map((row: any) => ({
          sku: row.SKU,
          asin: row.ASIN,
          fnsku: row.FNSKU,
        }));
        const newProducts: string[] = [];

        for (const { sku, asin, fnsku } of skuList) {
          if (sku) {
            const existingProduct = await this.productService.findProduct({
              sku,
            });
            if (existingProduct.product) {
              if (
                existingProduct.product.size == 8 &&
                existingProduct.product.color == 'WhiteRG'
              ) {
                await this.productService.updateProduct(
                  existingProduct.product,
                  {
                    sku: sku,
                    ASIN: asin ?? '-',
                    FNSKU: fnsku ?? '-',
                  },
                );
              }
            } else {
              newProducts.push(sku);
              this.logger.verbose(
                `New product - SKU: ${sku} | ASIN: ${asin} | FNSKU: ${fnsku}`,
              );
            }
          }
        }
        this.logger.warn(`New products: ${newProducts.join(', ')}`);
      }

      return true;
    } catch (error) {
      console.error('Error processing XLSX file:', error);
      throw new HttpException('Error processing XLSX file', 500);
    }
  }

  // Upload a file and process it
  async updateInventoryFromExcel(file: Express.Multer.File): Promise<boolean> {
    try {
      const workbook = xlsx.read(file.buffer, { type: 'buffer' });
      const sheets = Object.keys(workbook.Sheets);

      this.logger.warn(`Processing ${sheets.length} sheets`);

      for (let index = 0; index < sheets.length; index++) {
        const sheetName = sheets[index];
        const worksheet = workbook.Sheets[sheetName];
        const data = xlsx.utils.sheet_to_json(worksheet);

        const skuList = data.map((row: any) => ({
          sku: row.SKU,
          qty: row.QTY,
        }));
        const newProducts: { sku: string; qty: number }[] = [];

        this.logger.warn(`Processing ${skuList.length} rows`);

        for (const { sku, qty } of skuList) {
          if (sku && qty) {
            const existingProduct = await this.productService.findProduct({
              sku,
            });
            if (
              existingProduct.product &&
              existingProduct.product.inventory_item_id
            ) {
              this.logger.warn(
                `Updated product - SKU: ${sku} | QTY: ${qty} | Current QTY: ${existingProduct.product.shopify_quantity ?? 0}`,
              );
              const numberQty =
                (existingProduct.product.shopify_quantity ?? 0) > 0
                  ? existingProduct.product.shopify_quantity + qty
                  : qty;

              await this.updateSingleProduct({
                inventory_item_id: existingProduct.product.inventory_item_id,
                qty: numberQty,
              });
            } else {
              this.logger.warn(`New product - SKU: ${sku} | QTY: ${qty}`);
              newProducts.push({
                sku: sku,
                qty: qty,
              });
            }
          }
        }
      }

      return true;
    } catch (error) {
      console.error('Error processing XLSX file:', error);
      throw new HttpException('Error processing XLSX file', 500);
    }
  }
}
