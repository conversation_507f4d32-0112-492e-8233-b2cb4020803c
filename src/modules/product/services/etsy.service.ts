import { Injectable, Logger, OnModuleInit } from '@nestjs/common';
import { Cron, CronExpression } from '@nestjs/schedule';
import { InjectRepository } from '@nestjs/typeorm';
import axios, { AxiosInstance } from 'axios';
import createAuthRefreshInterceptor from 'axios-auth-refresh';
import { Etsy, IShopReceipt, IShopReceiptTransaction } from 'etsy-ts/v3';
import { decode } from 'html-entities';
import moment from 'moment-timezone';
import { ILike, Repository } from 'typeorm';
import { ApiConfigService } from '../../../common/services/api-config.service';
import { OrderEntity } from '../../../entities/order.entity';
import { CountryService } from '../../utils/country.util';
import { ConfigService } from '../../config/config.service';
import { ProductService } from './product.service';
import { ProductDto } from '../dto/product.dto';

@Injectable()
export class EtsyService implements OnModuleInit {
  private readonly logger = new Logger(EtsyService.name);
  private axiosHandler: AxiosInstance = axios.create();
  private v3Client: Etsy;

  constructor(
    private readonly configService: ConfigService,
    private readonly countryService: CountryService,
    private readonly productService: ProductService,
    private readonly appConfig: ApiConfigService,
    @InjectRepository(OrderEntity)
    private readonly orderRepository: Repository<OrderEntity>,
  ) {
    this.v3Client = new Etsy({ apiKey: this.appConfig.etsyApiKey });
    this.initializeAxiosHandler();
  }

  async onModuleInit() {
    if (this.appConfig.isProduction) {
      await this.generateAccessToken();
      await this.orderJob();
      await this.removeDuplicateOrders();
      await this.fetchPaidOrders();
    }
    await this.generateAccessToken();

    //  await this.orderJob();
  }

  /**
   * Initialize Axios Handler with Interceptors
   */
  private initializeAxiosHandler() {
    this.axiosHandler = axios.create({
      baseURL: this.appConfig.etsyRefreshUrl,
      headers: {
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Headers': 'Authorization',
        'x-api-key': this.appConfig.etsyApiKey,
      },
    });

    this.axiosHandler.interceptors.response.use(
      (response) => response,
      (error) => this.handleAxiosError(error),
    );

    createAuthRefreshInterceptor(
      this.v3Client.httpClient.instance as AxiosInstance,
      () => {
        this.logger.warn(
          'Refreshing access token from createAuthRefreshInterceptor',
        );
        return this.generateAccessToken();
      },
      { pauseInstanceWhileRefreshing: true, shouldRefresh: () => true },
    );
  }

  /**
   * Handle Axios Errors
   *
   * @param error - The error response from Axios
   * @returns A promise rejection with the error
   */
  private async handleAxiosError(error: any) {
    const { response, config } = error;
    if (response) {
      const { status } = response;
      this.logRequestDetails(config.url, config.params);
      switch (status) {
        case 429:
          this.logger.error('Too many requests, retrying...');
          await new Promise((resolve) => setTimeout(resolve, 5000));
          return this.retryRequest(config);
        case 503:
          this.logger.error('Service unavailable');
          break;
        default:
          this.logger.error(
            `Error: ${status} - ${JSON.stringify(response.data)}`,
          );
      }
    } else {
      this.logger.error('Network error:', error.message);
    }
    return Promise.reject(error);
  }

  /**
   * Retry Axios Request
   *
   * @param config - The Axios request configuration
   * @returns The result of the retried Axios request
   */
  private retryRequest(config: any) {
    return this.axiosHandler.request(config);
  }

  /**
   * Log Axios Request Details
   *
   * @param url - The request URL
   * @param params - The request parameters
   */
  private logRequestDetails(url: string, params: any) {
    this.logger.error(`Request URL: ${url}`);
    if (params) {
      this.logger.error('Request Params:', params);
    }
  }

  /**
   * Generate Etsy Access Token
   */
  async generateAccessToken() {
    this.logger.warn('Generating new access token');
    delete this.axiosHandler.defaults.headers['Authorization'];
    const { accessToken, newRefreshToken } = await this.generateEtsyToken();

    this.logger.warn('AccessToken:', accessToken);
    this.logger.warn('RefreshToken:', newRefreshToken);

    this.axiosHandler.defaults.headers['Authorization'] =
      `Bearer ${accessToken}`;

    this.v3Client = new Etsy({
      apiKey: this.appConfig.etsyApiKey,
      accessToken,
    });
  }

  /**
   * Generate Etsy Token
   *
   * @returns An object containing the access token and refresh token
   */
  async generateEtsyToken(): Promise<{
    accessToken: string;
    newRefreshToken: string;
  }> {
    const refreshToken = await this.configService.getConfig('etsyRefreshToken');
    const tokenResponse = await this.axiosHandler.post('public/oauth/token', {
      grant_type: 'refresh_token',
      client_id: this.appConfig.etsyApiKey,
      refresh_token: refreshToken || this.appConfig.etsyRefreshToken,
    });

    const { access_token: accessToken, refresh_token: newRefreshToken } =
      tokenResponse.data;
    await this.configService.setConfig('etsyRefreshToken', newRefreshToken);

    return { accessToken, newRefreshToken };
  }

  /**
   * Cron Jobs - Scheduled Functions
   */

  // Remove duplicate orders every day at 6 AM
  @Cron(CronExpression.EVERY_DAY_AT_6AM)
  async removeDuplicateOrders(): Promise<void> {
    const query = `
      SELECT external_order_id, COUNT(*)
      FROM orders o
      WHERE o."source" ILIKE 'etsy' AND external_created_at >= '2023-01-01'
      GROUP BY external_order_id
      HAVING COUNT(*) > 1;`;

    let deletedCount = 0;
    let duplicateCount = 0;

    const duplicateOrders = await this.orderRepository.query(query);
    this.logger.debug(`Found ${duplicateOrders.length} duplicate orders.`);

    for (const order of duplicateOrders) {
      this.logger.debug(`Checking order ${order.external_order_id}...`);

      const { external_order_id } = order;
      const duplicateInstances = await this.orderRepository.find({
        where: { externalOrderId: external_order_id, source: ILike('etsy') },
      });

      duplicateCount += duplicateInstances.length;
      if (duplicateInstances.length > 1) {
        const instancesToRemove = duplicateInstances.slice(1);
        for (const instance of instancesToRemove) {
          await this.orderRepository.remove(instance);
          deletedCount++;
        }
      }
    }

    this.logger.debug(
      `Deleted ${deletedCount} duplicate rows out of ${duplicateCount} duplicate instances found.`,
    );
  }

  // Fetch paid orders every day at 5 AM
  @Cron(CronExpression.EVERY_DAY_AT_5AM)
  async fetchPaidOrders() {
    try {
      const twoWeeksAgo = new Date();
      twoWeeksAgo.setDate(twoWeeksAgo.getDate() - 2);

      await this.generateAccessToken();
      const limit = 25;
      let page: number | null = 1;

      const params = {
        min_last_modified: Math.trunc(Number(moment(twoWeeksAgo).format('X'))),
      };

      while (page !== null) {
        const result = await this.v3Client.ShopReceipt.getShopReceipts({
          shopId: this.appConfig.etsyShopID,
          limit,
          offset: (page - 1) * limit,
          ...params,
        });

        const { data } = result;
        if (data.count === undefined) throw new Error('no count');
        if (data.count <= page * limit) page = null;
        else page += 1;
        if (!data.results) throw new Error('no results');

        data.results.forEach((item) => this.checkAndPersistOrders(item));
      }

      this.logger.log('Fetch paid orders complete');
    } catch (error: any) {
      this.logger.error(error.message);
    }
  }

  // Fetch orders every 2 hours between 6 AM and 11 PM
  @Cron('*/120 6-23 * * *')
  async orderJob() {
    this.logger.warn('Fetching orders');
    await this.generateAccessToken();

    try {
      const limit = 25;
      let page: number | null = 1;

      const latestOrder = await this.orderRepository
        .createQueryBuilder('order')
        .where('order.source = :source', { source: 'Etsy' })
        .orderBy('order.externalCreatedAt', 'DESC')
        .getOne();

      let params = {
        min_created: Math.trunc(Number(moment('2025-07-01').format('X'))),
      };

      if (latestOrder) {
        params = {
          min_created: Math.trunc(
            Number(moment(latestOrder.externalCreatedAt).format('X')),
          ),
        };
      }

      while (page !== null) {
        const result = await this.v3Client.ShopReceipt.getShopReceipts({
          shopId: this.appConfig.etsyShopID,
          limit,
          offset: (page - 1) * limit,
          ...params,
        });

        const { data } = result;
        if (data.count === undefined) throw new Error('no count');
        if (data.count <= page * limit) page = null;
        else page += 1;
        if (!data.results) throw new Error('no results');

        data.results.forEach((item) => this.checkAndPersistOrders(item));
      }

      this.logger.log('Fetch orders complete');
    } catch (error: any) {
      this.logger.error(error.message);
    }
  }

  /**
   * Helper Functions
   */

  /**
   * Check and Persist Orders
   *
   * @param order - The order to check and persist
   */
  private async checkAndPersistOrders(order: any): Promise<void> {
    const {
      receipt_id,
      created_timestamp,
      buyer_email,
      message_from_buyer,
      transactions,
      name,
      city,
      country_iso,
      state,
      status,
      subtotal,
    } = order as Required<IShopReceipt>;
    const orderId = `E${receipt_id}`;

    const prev = await this.orderRepository
      .createQueryBuilder('order')
      .where('order.order_id = :id', { id: orderId })
      .getOne();

    if (prev) {
      await this.orderRepository
        .createQueryBuilder()
        .delete()
        .from(OrderEntity)
        .where('order_id = :id', { id: orderId })
        .execute();
    }

    const isMixedOrder =
      transactions.some((t) => t.title?.toLowerCase().includes('engraved')) &&
      transactions.some((t) => !t.title?.toLowerCase().includes('engraved'));

    for (const [index, transaction] of transactions.entries()) {
      const itemId = `E${receipt_id}.${index}`;

      const createdTimeLocal = moment(created_timestamp * 1000)
        .tz('America/Vancouver')
        .format('YYYY-MM-DD HH:mm');
      const message = decode(message_from_buyer);
      const { quantity, sku, variations, price } =
        transaction as Required<IShopReceiptTransaction>;

      if (!sku) continue;

      let engravingSide: string | undefined;
      let personalization: string | undefined;

      for (const { property_id, formatted_value } of variations) {
        const decodedValue = decode(formatted_value);
        if (property_id === 54) {
          personalization = decodedValue;
          if (personalization === 'Not requested on this item.') {
            personalization = undefined;
          }
        }

        if (property_id === 513) {
          engravingSide = decodedValue;
        }
        if (property_id === 514 && formatted_value === 'Yes') {
          engravingSide = 'inside';
        }
      }

      let outside = {};
      let inside = {};
      let colorP: string | undefined;

      try {
        const product = await this.productService.findProduct({ sku });

        const { color, size, style, design } = product.productInfo;

        if (design) outside = { design };

        if (engravingSide) {
          const lines: string[] | undefined = personalization?.split(/\r?\n/);
          const engravingSideLowercase = engravingSide.toLowerCase();

          if (lines) {
            lines.forEach((e) => {
              try {
                if (e.toLowerCase().includes('inside')) {
                  inside = { design: e.split(':')[1].trim() };
                }
                if (e.toLowerCase().includes('outside')) {
                  outside = { design: e.split(':')[1].trim() };
                }
                if (
                  e.toLowerCase().includes('color') ||
                  e.toLowerCase().includes('colour')
                ) {
                  colorP = e.split(':')[1].trim();
                }
              } catch (error) {
                outside = { design: personalization };
                inside = { design: personalization };
              }
            });
          }

          if (
            engravingSideLowercase.includes('inside') &&
            !engravingSideLowercase.includes('outside')
          ) {
            if (Object.keys(inside).length === 0) {
              inside = { design: personalization };
            }
          } else if (
            engravingSideLowercase.includes('outside') &&
            !engravingSideLowercase.includes('inside')
          ) {
            if (Object.keys(outside).length === 0) {
              outside = { design: personalization };
            }
          }
        }

        const { amount, divisor, currency_code } = price;
        await this.productService.sellItem(sku);

        const orderItem = this.orderRepository.create({
          externalCreatedAt: createdTimeLocal,
          externalOrderId: String(itemId),
          externalPrice: (amount ?? 0) / (divisor ?? 1),
          externalCurrency: currency_code ?? '$',
          current_subtotal_price:
            index === 0 ? (subtotal.amount ?? 0) / (divisor ?? 1) : 0,
          order_number: 1,
          email: buyer_email ?? '',
          customer_name: name,
          order_id: orderId,
          item_no: `${index + 1}/${transactions.length}`,
          message: message ?? '-',
          isMixedOrder: isMixedOrder ?? false,
          status: status,
          outside: `${outside['design'] ?? ''}`,
          inside: `${inside['design'] ?? ''}`,
          size,
          saved: prev?.saved ?? false,
          goldInlay: '-',
          data: !color || !style ? JSON.stringify(transaction) : '',
          quantity,
          color: color !== '' && color ? color : (colorP ?? ''),
          sku,
          style,
          city: city ?? '',
          country: this.countryService.getCountryNameByCode(country_iso ?? ''),
          province: this.countryService.getStateName(
            this.countryService.getCountryNameByCode(country_iso ?? ''),
            state ?? '',
          ),
          source: 'Etsy',
        });

        const successful = await this.orderRepository.save(orderItem);
        this.logger.debug(`Inserted order item: ${successful.externalOrderId}`);
      } catch (e) {}
    }
  }

  async fetchListing() {
    await this.generateAccessToken();
    this.logger.warn('Fetching listings');
    const { data } = await this.axiosHandler.get(
      `application/shops/${this.appConfig.etsyShopID}/listings`,
    );

    if (data.count === undefined) throw new Error('no count');
    if (!data.results) throw new Error('no results');

    for (const { listing_id } of data.results) {
      this.logger.warn(`Fetching inventory for listing ${listing_id}`);
      await this.fetchInventory(listing_id);
    }
    this.logger.warn('Fetch listings complete');
  }

  async fetchInventory(listingId: string) {
    const { data } = await this.axiosHandler.get(
      `application/listings/${listingId}/inventory`,
    );

    for (const product of data.products) {
      const { sku, product_id } = product;
      const { offering_id, quantity } = product.offerings[0]; // Assuming one offering per product

      const newOffering = {
        listingId,
        product_id,
        offering_id,
        quantity,
      };

      const findProduct = await this.productService.findProduct({ sku });

      if (findProduct.product) {
        let existingOfferings: any[] = [];

        // Check if the product already has etsy_offerings
        if (findProduct.product.etsy_offerings) {
          existingOfferings = JSON.parse(findProduct.product.etsy_offerings);
        }
        // Check if the new offering already exists in the list
        const offeringExists = existingOfferings.some(
          (offering) => offering.offering_id === newOffering.offering_id,
        );

        // Only add the new offering if it doesn't already exist
        if (!offeringExists) {
          existingOfferings.push(newOffering);
        }
        // Update etsy_offerings with the new list
        await this.productService.updateEtsy({
          product: findProduct.product,
          etsy_offerings: JSON.stringify(existingOfferings),
        });
      } else {
        this.logger.warn(`Product not found for SKU: ${sku}`);
      }
    }
  }

  /**
   * Update the quantities of multiple SKUs in Etsy.
   *
   * @param product - The product entity containing the SKUs to update.
   * @param quantity - The new quantity to set for each SKU.
   */
  async updateListingQuantities(products: ProductDto[]): Promise<void> {
    try {
      await this.generateAccessToken();
      this.logger.warn('Updating listing quantities');
      for (const product of products) {
        await new Promise((resolve) => setTimeout(resolve, 500));

        if (!product.etsy_offerings) {
          this.logger.warn(
            `No Etsy offerings found for product: ${product.sku}`,
          );
          continue;
        }

        let offerings: any[];
        try {
          offerings = JSON.parse(product.etsy_offerings);
        } catch (error) {
          this.logger.error(
            `Error parsing etsy_offerings for product ${product.sku}`,
          );
          continue;
        }

        if (!Array.isArray(offerings) || offerings.length === 0) {
          this.logger.warn(
            `Invalid or empty Etsy offerings for product: ${product.sku}`,
          );
          continue;
        }

        for (const offering of offerings) {
          if (!offering.listingId || !offering.offering_id) {
            this.logger.warn(
              `Invalid offering data for product ${product.sku}: ${JSON.stringify(offering)}`,
            );
            continue;
          }

          // Prepare the update data for this offering
          const updateData = {
            products: [
              {
                // sku: product.sku,
                offerings: [
                  {
                    id: offering.offering_id,
                    quantity: product.shopify_quantity,
                  },
                ],
              },
            ],
          };

          this.logger.warn(updateData);
          // const { data } = await this.axiosHandler.put(
          //   `application/listings/${offering.listingId}/inventory`,
          //   updateData,
          // );
        }
      }
    } catch (error: any) {
      this.logger.error(`Error updating listing quantities: ${error.message}`);
    }
  }

  /**
   * Request an authorization code from Etsy
   *
   * This method generates a URL for requesting an authorization code from Etsy's OAuth system.
   * The user needs to visit this URL to grant permissions to the application.
   *
   * @see https://developer.etsy.com/documentation/essentials/authentication#step-1-request-an-authorization-code
   */
  async requestAuthorizationCode(): Promise<string> {
    const params = new URLSearchParams({
      response_type: 'code',
      client_id: this.appConfig.etsyApiKey,
      redirect_uri: 'https://0ppyvbq3r4.execute-api.ca-central-1.amazonaws.com',
      scope: [
        'shops_r',
        'shops_w',
        'transactions_r',
        'transactions_w',
        'listings_r',
        'listings_w',
      ].join(' '),
      state: 'STATE',
      code_challenge: 'DSWlW2Abh-cf8CeLL8-g3hQ2WQyYdKyiu83u_s7nRhI',
      code_challenge_method: 'S256',
    });

    const authorizationUrl = `https://www.etsy.com/oauth/connect?${params.toString()}`;
    this.logger.log(`Authorization URL: ${authorizationUrl}`);
    this.logger.log('Please visit this URL to authorize the application.');
    return authorizationUrl;
  }

  /**
   * Request an access token from Etsy
   *
   * This method exchanges an authorization code for an access token and refresh token.
   * It should be called after the user has granted permissions and Etsy has redirected
   * back to our application with an authorization code.
   *
   * @param code The authorization code received from Etsy
   * @returns An object containing the access token and refresh token
   * @see https://developer.etsy.com/documentation/essentials/authentication#step-3-request-an-access-token
   */
  async requestAccessToken(code: string): Promise<{
    accessToken: string;
    refreshToken: string;
  }> {
    const formData = new FormData();
    formData.append('grant_type', 'authorization_code');
    formData.append('client_id', this.appConfig.etsyApiKey);
    formData.append(
      'redirect_uri',
      'https://0ppyvbq3r4.execute-api.ca-central-1.amazonaws.com',
    );
    formData.append('code', code);
    formData.append(
      'code_verifier',
      'vvkdljkejllufrvbhgeiegrnvufrhvrffnkvcknjvfid',
    );

    try {
      const response = await this.axiosHandler.post(
        'public/oauth/token',
        formData,
        {
          headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
          },
        },
      );

      const { access_token: accessToken, refresh_token: refreshToken } =
        response.data;

      this.logger.log('Successfully obtained new access and refresh tokens');
      await this.configService.setConfig('etsyRefreshToken', refreshToken);

      return { accessToken, refreshToken };
    } catch (error) {
      this.logger.error('Failed to request access token', error);
      throw new Error('Failed to request access token from Etsy');
    }
  }
}
