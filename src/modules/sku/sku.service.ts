import { Injectable, Inject } from '@nestjs/common';
import { Repository } from 'typeorm';
import { ColorEntity } from '../../entities/color.entity';
import { ColorNameEntity } from '../../entities/color-name.entity';
import { DesignEntity } from '../../entities/design.entity';
import { DesignNameEntity } from '../../entities/design-name.entity';
import { CACHE_MANAGER } from '@nestjs/cache-manager';
import { Cache } from 'cache-manager';
import { RingInfo } from '../../common/interfaces/RingInfo';
import { getRingStyle } from '../utils/ring-style.util';
import { getColor } from '../utils/colors.util';
import { extractNumberFromSku } from '../utils/sizes.util';
import { getDesignFromSku } from '../utils/designs.util';
import { InjectRepository } from '@nestjs/typeorm';

@Injectable()
export class SkuService {
  constructor(
    @InjectRepository(ColorEntity)
    private readonly colorRepository: Repository<ColorEntity>,
    @InjectRepository(ColorNameEntity)
    private readonly colorNameRepository: Repository<ColorNameEntity>,
    @InjectRepository(DesignEntity)
    private readonly designRepository: Repository<DesignEntity>,
    @InjectRepository(DesignNameEntity)
    private readonly designNameRepository: Repository<DesignNameEntity>,
    @Inject(CACHE_MANAGER) private readonly cacheService: Cache,
  ) {}

  /**
   * Extract SKU information
   *
   * @param sku - The SKU to be processed
   * @returns RingInfo - Extracted information about the ring
   */
  async extractSku(sku: string): Promise<RingInfo> {
    try {
      const cacheKey = `extractSku/${sku}`;
      const cachedData: any = await this.cacheService.get(cacheKey);

      if (cachedData) return cachedData;

      // Extract style and size from SKU
      const style = getRingStyle(sku ?? '') ?? '';
      const s = extractNumberFromSku(sku ?? '');
      const size = Number.isInteger(s) ? s : 0;

      let design = '';
      let color = '';

      // Extract color from SKU
      try {
        color =
          (await getColor(
            this.colorRepository,
            this.colorNameRepository,
            sku,
          )) ?? '';
      } catch (e) {
        console.log(e);
      }

      // Extract design from SKU
      try {
        design =
          (await getDesignFromSku(
            this.designRepository,
            this.designNameRepository,
            sku,
          )) ?? '';
      } catch (e) {
        console.log(e);
      }

      // Construct the result object
      const result: RingInfo = {
        style: style ?? '',
        color: color ?? '',
        size: size ?? 0,
        design: design ?? '',
      };

      // Cache the result
      await this.cacheService.set(cacheKey, result, 1800);

      return result;
    } catch (e) {
      console.log(e);
      return {
        style: `Error ${sku}`,
        color: `Error ${e}`,
        size: 0,
        design: `Error ${e}`,
      };
    }
  }
}
