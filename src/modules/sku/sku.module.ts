import { <PERSON>du<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { ColorEntity } from '../../entities/color.entity';
import { ColorNameEntity } from '../../entities/color-name.entity';
import { DesignEntity } from '../../entities/design.entity';
import { DesignNameEntity } from '../../entities/design-name.entity';
import { SkuService } from './sku.service';

@Module({
  imports: [
    TypeOrmModule.forFeature([
      ColorEntity,
      ColorNameEntity,
      DesignEntity,
      DesignNameEntity,
    ]),
  ],
  providers: [SkuService],
  exports: [SkuService],
})
export class SkuModule {}
