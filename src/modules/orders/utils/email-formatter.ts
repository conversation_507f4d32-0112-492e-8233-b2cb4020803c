export function formatOutOfStockProducts(items: any[]): string {
  return items
    .map(
      (item) => `
      <tr>
        <td style="border: 1px solid #ddd; padding: 8px;">${item.p_style}</td>
        <td style="border: 1px solid #ddd; padding: 8px;">${item.p_color}</td>
        <td style="border: 1px solid #ddd; padding: 8px;">${item.p_size}</td>
        <td style="border: 1px solid #ddd; padding: 8px;">${item.p_oos_date
          .toISOString()
          .slice(0, 10)}</td>
      </tr>`,
    )
    .join('');
}

export function generateOutOfStockEmailBody(
  formattedItems: string,
  outOfStockCount: number,
): string {
  return `
  <h1>Daily Out of Stock Products</h1>
  <p>We have ${outOfStockCount} items that became out of stock today. Please review the details below:</p>
  <table style="width: 100%; border-collapse: collapse;">
    <thead style="background-color: lightgrey; text-align: left;">
      <tr>
        <th style="border: 1px solid #ddd; padding: 8px;">Style</th>
        <th style="border: 1px solid #ddd; padding: 8px;">Color</th>
        <th style="border: 1px solid #ddd; padding: 8px;">Size</th>
        <th style="border: 1px solid #ddd; padding: 8px;">Out of Stock Date</th>
      </tr>
    </thead>
    <tbody>
      ${formattedItems}
    </tbody>
  </table>`;
}
