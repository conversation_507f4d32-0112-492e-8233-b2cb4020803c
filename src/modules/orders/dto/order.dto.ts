import { ApiProperty } from '@nestjs/swagger';
import moment from 'moment-timezone';

import { AbstractDto } from '../../../common/dto/abstract.dto';
import { type OrderEntity } from '../../../entities/order.entity';

export class OrderDto extends AbstractDto {
  @ApiProperty()
  externalOrderId!: string;

  @ApiProperty()
  source!: string;

  @ApiProperty()
  current_subtotal_price?: number;

  @ApiProperty()
  total_discounts?: number;

  @ApiProperty()
  total_shipping_price?: number;

  @ApiProperty()
  total_tax?: number;

  @ApiProperty()
  price?: number;

  @ApiProperty()
  order_number!: number;

  @ApiProperty()
  email?: string;

  @ApiProperty()
  data?: string;

  @ApiProperty()
  isMixedOrder?: boolean;

  @ApiProperty()
  outside?: string;

  @ApiProperty()
  inside?: string;

  @ApiProperty()
  size?: number;

  @ApiProperty()
  sku?: string;

  @ApiProperty()
  color?: string;

  @ApiProperty()
  fulfillmentChannel?: string;

  @ApiProperty()
  salesChannel?: string;

  @ApiProperty()
  goldInlay?: string;

  @ApiProperty()
  quantity?: number;

  @ApiProperty()
  order_id!: string;

  @ApiProperty()
  note?: string;

  @ApiProperty()
  message?: string;

  engrave_order?: boolean;

  saved?: boolean;

  @ApiProperty()
  customer_name?: string;

  @ApiProperty()
  style?: string;

  @ApiProperty()
  item_no?: string;

  @ApiProperty()
  status?: string;

  @ApiProperty()
  city?: string;

  @ApiProperty()
  province?: string;

  @ApiProperty()
  country?: string;

  @ApiProperty()
  externalCreatedAt?: Date;

  @ApiProperty()
  externalPrice!: string;

  constructor(orderEntity: OrderEntity) {
    super(orderEntity);
    this.createdAt = moment(orderEntity.externalCreatedAt).format(
      'YYYY/MM/DD HH:mm:ss',
    );
    this.externalCreatedAt = orderEntity.externalCreatedAt;
    this.source = orderEntity.source;
    // this.current_subtotal_price = orderEntity.current_subtotal_price;
    // this.total_discounts = orderEntity.total_discounts;
    // this.total_shipping_price = orderEntity.total_shipping_price;
    // this.total_tax = orderEntity.total_tax;
    // this.price = orderEntity.price;
    // this.isMixedOrder = orderEntity.isMixedOrder;
    // this.order_number = orderEntity.order_number;
    this.email = orderEntity.email;
    // this.salesChannel = orderEntity.salesChannel;
    this.outside = orderEntity.outside;
    this.inside = orderEntity.inside;
    this.externalOrderId = orderEntity.externalOrderId;
    this.order_id = orderEntity.order_id;
    // this.engrave_order = orderEntity.engrave_order;
    this.note = orderEntity.note;
    this.message = orderEntity.message;
    // this.fulfillmentChannel = orderEntity.fulfillmentChannel;
    this.item_no = orderEntity.item_no;
    this.customer_name = orderEntity.customer_name;
    this.style = orderEntity.style;
    this.size = orderEntity.size;
    this.color = orderEntity.color;
    this.sku = orderEntity.sku;
    // this.goldInlay = orderEntity.goldInlay;
    this.quantity = orderEntity.quantity;
    // this.data = orderEntity.data;
    this.externalPrice = `${orderEntity.externalPrice} ${orderEntity.externalCurrency}`;
    this.status = orderEntity.status;
    // this.city = orderEntity.city;
    // this.province = orderEntity.province;
    // this.country = orderEntity.country;
  }
}
