import { Inject, Injectable, Logger, OnModuleInit } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Brackets, Equal, ILike, In, MoreThan, Repository } from 'typeorm';
import { PageDto } from '../../../common/dto/page.dto';
import { SearchQuery } from '../../../common/dto/page-options.dto';
import { type OrderDto } from '../dto/order.dto';
import { type PaginateOrdersOptionsDto } from '../dto/paginate-order-options.dto';
import { type PaginateProductsOptionsDto } from '../dto/paginate-product-options.dto';
import { OrderEntity } from '../../../entities/order.entity';
import { RingInfo } from '../../../common/interfaces/RingInfo';
import { convertToSQLDateTime, formatDate } from '../../utils/date.util';
import { <PERSON><PERSON>, CronExpression } from '@nestjs/schedule';
import { subDays, startOfDay } from 'date-fns';
import { ApiConfigService } from '../../../common/services/api-config.service';
import { CACHE_MANAGER } from '@nestjs/cache-manager';
import { Cache } from 'cache-manager';
import { SalesDataQuery } from '../types/SalesDataQuery';
import { CountPerDate } from '../types/CountPerDate';
import { SalesVelocityDataQuery } from '../types/SalesVelocityData';
import { SoldProductEntity } from '../../../entities/sold_product.entity';
import { SkuService } from '../../sku/sku.service';
import axios, { AxiosInstance } from 'axios';
import { DesignEntity } from '../../../entities/design.entity';
import { DesignNameEntity } from '../../../entities/design-name.entity';

@Injectable()
export class DataService implements OnModuleInit {
  private readonly logger = new Logger(DataService.name);
  private axiosHandler: AxiosInstance = axios.create();

  constructor(
    private readonly appConfig: ApiConfigService,
    @InjectRepository(OrderEntity)
    private readonly orderRepository: Repository<OrderEntity>,
    @InjectRepository(DesignEntity)
    private readonly designRepository: Repository<DesignEntity>,
    @InjectRepository(DesignNameEntity)
    private readonly designNameRepository: Repository<DesignNameEntity>,
    @InjectRepository(SoldProductEntity)
    private readonly soldProductRepository: Repository<SoldProductEntity>,
    @Inject(CACHE_MANAGER) private cacheService: Cache,
    private readonly skuService: SkuService,
  ) {
    this.axiosHandler = axios.create({
      baseURL: 'https://ba8fhw5eug.execute-api.us-west-2.amazonaws.com',
      headers: {
        'x-custom-api-key': 'b39snz92naoiwe8h2',
      },
    });
  }

  async onModuleInit() {
    await this.cacheService.reset();
    await this.getOrdersWithMatchedColors();
    // await this.updateOrdersDaily();
  }

  async getMostDesignsFromOrders(startDate: Date, endDate: Date): Promise<any> {
    const designs = await this.orderRepository
      .createQueryBuilder('o')
      .select(['o.outside as design', `SUM(o.quantity) AS qty`])
      .where('o.external_created_at BETWEEN :startDate AND :endDate', {
        startDate,
        endDate,
      })
      .andWhere(`o.outside != ''`)
      .andWhere('(o.status IS NULL OR o.status != :canceled)', {
        canceled: 'Canceled',
      })
      .groupBy('o.outside')
      .orderBy('qty', 'DESC')
      .getRawMany();

    return designs;
  }

  async getTotalSalesAndQuantities(
    startDate: Date,
    endDate: Date,
  ): Promise<{ totalOrders: number; totalQuantities: number }> {
    const result = await this.orderRepository
      .createQueryBuilder('order')
      .select([
        'COUNT(DISTINCT order.order_id) AS totalOrders',
        'SUM(order.quantity) AS totalQuantities',
      ])
      .where('order.externalCreatedAt BETWEEN :start AND :end', {
        start: startDate,
        end: endDate,
      })
      .andWhere('(order.status IS NULL OR order.status != :canceled)', {
        canceled: 'Canceled',
      })
      .getRawOne();

    return {
      totalOrders: parseInt(result.totalorders || '0', 10),
      totalQuantities: parseFloat(result.totalquantities || '0'),
    };
  }

  calculatePercentageChange(
    currentValue: number,
    previousValue: number,
  ): { percentage: number; isIncrease: boolean } {
    if (previousValue === 0) return { percentage: 100, isIncrease: true };
    const percentage = ((currentValue - previousValue) / previousValue) * 100;

    return {
      percentage: Math.abs(percentage),
      isIncrease: percentage > 0,
    };
  }

  compareRings(currentPeriod: any[], previousPeriod: any[]): any[] {
    const previousMap = new Map(
      previousPeriod.map((item) => [
        `${item.style}-${item.color}-${item.size}`,
        item.qty,
      ]),
    );

    return currentPeriod.map((item) => {
      const key = `${item.style}-${item.color}-${item.size}`;
      const previousQty = previousMap.get(key) || 0;
      const change = this.calculatePercentageChange(item.qty, previousQty);
      return { ...item, change, previousQty };
    });
  }

  compareDesigns(currentPeriod: any[], previousPeriod: any[]): any[] {
    const previousMap = new Map(
      previousPeriod.map((item) => [`${item.design}`, item.qty]),
    );

    return currentPeriod.map((item) => {
      const key = `${item.design}`;
      const previousQty = previousMap.get(key) || 0;
      const change = this.calculatePercentageChange(item.qty, previousQty);
      return { ...item, change, previousQty };
    });
  }

  compareSales(currentPeriod: any[], previousPeriod: any[]): any[] {
    const previousMap = new Map(
      previousPeriod.map((item) => [`${item.name}`, item.sales]),
    );

    return currentPeriod.map((item) => {
      const key = `${item.name}`;
      const previousQty = previousMap.get(key) || 0;
      const change = this.calculatePercentageChange(item.sales, previousQty);
      return { ...item, change, previousQty };
    });
  }

  async getSalesByPlatform(startDate: Date, endDate: Date): Promise<any[]> {
    const results = await this.orderRepository
      .createQueryBuilder('order')
      .select([
        "COUNT(DISTINCT CASE WHEN LOWER(order.source) = 'shopify' THEN order.order_id END) as shopify",
        "COUNT(DISTINCT CASE WHEN order.sales_channel ILIKE 'amazon.com' AND order.fulfillment_channel ILIKE 'mfn' THEN order.order_id END) as fbm",
        "COUNT(DISTINCT CASE WHEN order.sales_channel ILIKE 'amazon.com' AND order.fulfillment_channel ILIKE 'afn' THEN order.order_id END) as fba",
        "COUNT(DISTINCT CASE WHEN LOWER(order.source) = 'etsy' THEN order.order_id END) as etsy",
      ])
      .where('order.externalCreatedAt BETWEEN :start AND :end', {
        start: startDate,
        end: endDate,
      })
      .andWhere('(order.status IS NULL OR order.status != :canceled)', {
        canceled: 'Canceled',
      })
      // .andWhere(`(order.status != 'Canceled' or order.status is null)`)

      .getRawOne();

    return [
      { name: 'Shopify', sales: parseInt(results.shopify) },
      { name: 'Etsy', sales: parseInt(results.etsy) },
      { name: 'Amazon FBM', sales: parseInt(results.fbm) },
      { name: 'Amazon FBA', sales: parseInt(results.fba) },
    ];
  }

  async getTotalSales(startDate: Date, endDate: Date): Promise<number> {
    const totalSales = await this.orderRepository
      .createQueryBuilder('order')
      .select('Count(DISTINCT order.order_id)', 'total')
      .where('order.externalCreatedAt BETWEEN :start AND :end', {
        start: startDate,
        end: endDate,
      })
      .andWhere('(order.status IS NULL OR order.status != :canceled)', {
        canceled: 'Canceled',
      })
      .getRawOne();

    return parseFloat(totalSales.total || '0');
  }

  async getOrdersWithMatchedColors(): Promise<any[]> {
    try {
      const matchedOrders = await this.orderRepository
        .createQueryBuilder('o')
        .leftJoin('color_names', 'cn', 'o.color ILIKE cn.value')
        .leftJoin('colors', 'c', 'cn.color_id = c.id')
        .select([
          'o.id AS orderId',
          'o.color AS orderColor',
          'c.color AS matchedColor',
        ])
        .where('cn.value IS NOT NULL')
        .getRawMany();

      this.logger.debug(`Matched Orders: ${matchedOrders.length} `);

      for (const order of matchedOrders) {
        if (order.ordercolor != order.matchedcolor) {
          try {
            await this.updateOrderColor(
              order.orderid,
              order.ordercolor,
              order.matchedcolor,
            );
          } catch (error) {
            this.logger.error(
              `Error updating order ID ${order.orderid}:`,
              error,
            );
          }
        }
      }

      return matchedOrders;
    } catch (error) {
      this.logger.error('Error fetching matched orders:', error);
      throw new Error('Error fetching matched orders');
    }
  }

  async getOrdersWithMatchedDesigns(): Promise<any[]> {
    try {
      const matchedOrders = await this.orderRepository
        .createQueryBuilder('o')
        .leftJoin('design_names', 'dn', 'o.outside ILIKE dn.value')
        .leftJoin('designs', 'd', 'dn.design_id = d.id')
        .select([
          'o.id AS orderId',
          'o.outside AS orderDesign',
          'd.design AS matchedDesign',
        ])
        .where('dn.value IS NOT NULL')
        .andWhere("dn.value <> ''")
        .andWhere('o.outside IS NOT NULL')
        .andWhere("o.outside <> ''")
        .getRawMany();

      this.logger.debug(`Matched Orders: ${matchedOrders.length}`);

      for (const order of matchedOrders) {
        if (order.orderdesign != order.matcheddesign) {
          try {
            await this.updateOrderDesign(
              order.orderid,
              order.orderdesign,
              order.matcheddesign,
            );
          } catch (error) {
            this.logger.error(
              `Error updating order ID ${order.orderid}:`,
              error,
            );
          }
        }
      }

      return matchedOrders;
    } catch (error) {
      this.logger.error('Error fetching matched orders:', error);
      throw new Error('Error fetching matched orders');
    }
  }

  private async updateOrderColor(
    orderId: number,
    orderColor: string,
    newColor: string,
  ): Promise<void> {
    try {
      await this.orderRepository
        .createQueryBuilder()
        .update(OrderEntity)
        .set({ color: newColor })
        .where('id = :orderId', { orderId })
        .execute();

      this.logger.debug(
        `Updated Order ID ${orderId} with color ${orderColor} to ${newColor}`,
      );
    } catch (error) {
      this.logger.error(`Error updating order ID ${orderId}:`, error);
      throw new Error(`Error updating order ID ${orderId}`);
    }
  }

  private async updateOrderDesign(
    orderId: number,
    orderDesign: string,
    newDesign: string,
  ): Promise<void> {
    try {
      if (orderDesign != newDesign) {
        await this.orderRepository
          .createQueryBuilder()
          .update(OrderEntity)
          .set({ outside: newDesign })
          .where('id = :orderId', { orderId })
          .execute();

        this.logger.debug(
          `Updated Order ID ${orderId} with design ${orderDesign} to ${newDesign}`,
        );
      }
    } catch (error) {
      this.logger.error(`Error updating order ID ${orderId}:`, error);
      throw new Error(`Error updating order ID ${orderId}`);
    }
  }

  async getAllOrders(
    paginateOptions: PaginateOrdersOptionsDto,
    data?: string,
  ): Promise<PageDto<OrderDto>> {
    const cacheKey = JSON.stringify(
      `getAllOrders${JSON.stringify(paginateOptions)}/${data}`,
    );

    const cachedData: any = await this.cacheService.get(cacheKey);
    if (cachedData) {
      this.logger.debug('cashedData');
      return cachedData;
    }

    const queryBuilder = await this.getQueryBuilder(data);
    const sortKey = paginateOptions.orderBy ?? 'externalCreatedAt';
    const sortOrder = paginateOptions.order;

    if (sortKey && sortOrder) {
      const isValidSortKey = this.orderRepository.metadata.columns.some(
        (column) => column.propertyName === sortKey,
      );

      if (isValidSortKey) {
        queryBuilder.orderBy(
          `order.${sortKey}`,
          sortOrder.toUpperCase() as 'ASC' | 'DESC',
        );
      }
    }

    console.log(paginateOptions);
    const [items, pageMetaDto] = await queryBuilder.paginate(paginateOptions);
    const response = items.toPageDto<OrderDto>(pageMetaDto);
    await this.cacheService.set(cacheKey, response, 300);
    return response;
  }

  async getAllOrderInfo(data?: string) {
    const qty = await (await this.getQueryBuilder(data))
      .select('SUM(quantity)')
      .getRawOne();

    const average = await (await this.getQueryBuilder(data))
      .select('AVG(current_subtotal_price) as avg')
      .getRawOne();

    const total = await (await this.getQueryBuilder(data))
      .select('SUM(current_subtotal_price)', 'sum')
      .getRawOne();

    const totalSeprated = await (await this.getQueryBuilder(data))
      .select('SUM(current_subtotal_price), external_currency')
      .where(`external_currency ilike 'USD'`)
      .groupBy('external_currency')
      .getRawMany();

    const totalOrders = await (await this.getQueryBuilder(data))
      .select('COUNT(DISTINCT order_id) as totalDistinctOrders')
      .getRawOne();

    const avgQty = await (await this.getQueryBuilder(data))
      .select('AVG(quantity) as avg')
      .getRawOne();

    return {
      qty: qty ? qty.sum : 0,
      avgQty: avgQty ? parseFloat(avgQty.avg).toFixed(2) : 0,
      total: total ? parseFloat(total.sum).toFixed(2) : 0,
      average: average ? parseFloat(average.avg).toFixed(2) : 0,
      totalOrders: totalOrders ? parseInt(totalOrders.totaldistinctorders) : 0,
      totalSeprated: totalSeprated,
    };
  }

  async getOrderStatistics(
    paginateOptions: PaginateProductsOptionsDto,
    sortField: string,
    sortOrder: 'ASC' | 'DESC',
    data?: string,
  ): Promise<PageDto<any>> {
    const { take: PAGE_SIZE, page: pageNumber } = paginateOptions;
    const jsonData = JSON.parse(data ?? '');

    const searchQuery = jsonData.query;

    let searchCondition = '';
    if (searchQuery) {
      const escapedQuery = searchQuery.replace(/'/g, "''");
      const intQuery = parseInt(searchQuery, 10);
      searchCondition = `
      AND (
        o.sku ILIKE '%${escapedQuery}%' OR
        o.outside ILIKE '%${escapedQuery}%' OR
        o.color ILIKE '%${escapedQuery}%' OR
        (o.size = ${
          isNaN(intQuery) ? 'NULL' : intQuery
        }) OR o.email ILIKE '%${escapedQuery}%' OR
        o.customer_name ILIKE '%${escapedQuery}%'
      )
    `;
    }

    const baseQuery = `
    SELECT
      o.email,
      o.customer_name,
      SUM(o.external_price) AS total,
      COUNT(DISTINCT o.order_id) AS order_count,
      COALESCE(
        TO_CHAR(
          ROUND(
            AVG(
              DATE_PART('year', age(o.external_created_at)) * 12 +
              DATE_PART('month', age(o.external_created_at))
            )::numeric, 1
          ),
          'FM999999999.0'
        ),
        'N/A'
      ) AS frequently_date,
      JSON_AGG(
        JSON_BUILD_OBJECT(
          'sku', o.sku,
          'created_at', o.external_created_at
        ) ORDER BY o.external_created_at
      ) AS created_at_skus
    FROM
      orders o
    WHERE
      o.source ILIKE 'shopify'
      AND o.status ILIKE 'fulfilled'
      AND o.external_price > 0
      ${searchCondition}
    GROUP BY
      o.email, o.customer_name
    ORDER BY ${sortField} ${sortOrder}
  `;

    const paginatedQuery = `
    ${baseQuery}
    ${
      PAGE_SIZE > 0
        ? `LIMIT ${PAGE_SIZE} OFFSET ${(pageNumber - 1) * PAGE_SIZE}`
        : ''
    }
  `;

    const responseData = await this.orderRepository.query(paginatedQuery);

    const countQuery = `
    SELECT
      COUNT(*) AS total_count
    FROM (
      SELECT DISTINCT o.email, o.customer_name
      FROM orders o
      WHERE
        o.source ILIKE 'shopify'
        AND o.status ILIKE 'fulfilled'
        AND o.external_price > 0
        ${searchCondition}
    ) subquery;
  `;

    const [{ total_count: totalCount }] =
      await this.orderRepository.query(countQuery);
    const totalCountInt = parseInt(totalCount, 10);

    const pageCount = Math.ceil(totalCountInt / PAGE_SIZE);
    const response = new PageDto(responseData, {
      hasNextPage: pageNumber < pageCount,
      hasPreviousPage: pageNumber > 1,
      itemCount: totalCountInt,
      page: pageNumber,
      take: PAGE_SIZE,
      pageCount: pageCount,
    });

    return response;
  }

  async unsavedItems(
    paginateOptions: PaginateProductsOptionsDto,
    data?: string,
  ): Promise<PageDto<OrderDto>> {
    const queryBuilder = this.orderRepository.createQueryBuilder('order');
    if (data) {
      const jsonData = JSON.parse(data);
      const { date } = jsonData;
      const startTime = convertToSQLDateTime(
        date,
        false,
        this.appConfig.isDevelopment,
      );
      const endTime = convertToSQLDateTime(
        date,
        true,
        this.appConfig.isDevelopment,
      );

      queryBuilder.where(
        `order.externalCreatedAt BETWEEN '${startTime}' AND '${endTime}'`,
      );

      const sourceConditions: any = [];
      sourceConditions.push(
        `LOWER(order.source) LIKE '%shopify%' and order.outside !=''`,
      );
      sourceConditions.push(
        `(order.sales_channel ILIKE 'amazon.com' AND order.fulfillmentChannel ILIKE '%mfn%')`,
      );
      sourceConditions.push(`LOWER(order.source) LIKE '%etsy%'`);

      if (sourceConditions.length > 0) {
        queryBuilder.andWhere(
          new Brackets((qb) => {
            sourceConditions.forEach((condition, index) => {
              if (index === 0) {
                qb.andWhere(condition);
              } else {
                qb.orWhere(condition);
              }
            });
          }),
        );
      }
    }

    queryBuilder.andWhere(`order.saved = false`);
    const [items, pageMetaDto] = await queryBuilder.paginate(paginateOptions);
    const response = items.toPageDto<OrderDto>(pageMetaDto);
    return response;
  }

  async getSalesInfo(
    paginateOptions: PaginateProductsOptionsDto,
    data?: string,
  ) {
    const jsonData = JSON.parse(data ?? '');
    const PAGE_SIZE = paginateOptions.take;
    const pageNumber = paginateOptions.page;

    // Calculate the number of months between start and end dates
    const startDate = new Date(jsonData.startDate || new Date());
    const endDate = new Date(jsonData.endDate || new Date());
    const monthsDiff =
      (endDate.getFullYear() - startDate.getFullYear()) * 12 +
      (endDate.getMonth() - startDate.getMonth());

    const months = monthsDiff > 0 ? monthsDiff : (jsonData.dateRange ?? 3);

    const multiplier =
      jsonData['multiplier'] ?? (months <= 3 ? 2 : months <= 6 ? 1 : 6);
    this.logger.verbose(
      `Calculated Months: ${months}, Multiplier: ${multiplier}`,
    );

    const cacheKey = JSON.stringify(
      `sales_info__${JSON.stringify(paginateOptions)}/${data}`,
    );

    const cachedData: any = await this.cacheService.get(cacheKey);
    if (cachedData) {
      this.logger.debug('Using cached data');
      // return cachedData;
    }

    const formattedStartDate = convertToSQLDateTime(
      startDate,
      false,
      this.appConfig.isDevelopment,
    );
    const formattedEndDate = convertToSQLDateTime(
      endDate,
      true,
      this.appConfig.isDevelopment,
    );

    const {
      shopify = false,
      etsy = false,
      fbm = false,
      fba = false,
    } = jsonData.platforms || {};

    const selectFields = [
      'p.style as style',
      'p.size as size',
      'p.color as color',
      'GREATEST(p.shopify_quantity, 0) AS local_inventory',
      'GREATEST(p.amazon_quantity, 0) AS fba_inventory',
      'p.asin as asin',
      'p.fnsku as fnsku',
      'p.sku as sku',
    ];

    if (shopify) {
      selectFields.push(
        `SUM(CASE WHEN o.source ILIKE 'shopify' THEN o.quantity ELSE 0 END) as shopify_sales`,
      );
    }
    if (etsy) {
      selectFields.push(
        `SUM(CASE WHEN o.source ILIKE 'etsy' THEN o.quantity ELSE 0 END) as etsy_sales`,
      );
    }

    if (fbm) {
      selectFields.push(
        `SUM(CASE WHEN o.sales_channel ILIKE 'amazon.com' AND o.fulfillment_channel ILIKE '%mfn%' THEN o.quantity ELSE 0 END) as fbm_sales`,
      );
    }

    if (fba) {
      selectFields.push(
        `SUM(CASE WHEN o.sales_channel ILIKE 'amazon.com' AND o.fulfillment_channel ILIKE '%afn%' THEN o.quantity ELSE 0 END) as fba_sales`,
      );
    }

    const getLocalOrderQty = () => {
      var a = '';

      var a = '';
      if (shopify) {
        a += `SUM(CASE WHEN o.source ILIKE 'shopify' THEN o.quantity ELSE 0 END)`;
      }
      if (etsy) {
        if (a != '') {
          a += ' + ';
        }
        a += `SUM(CASE WHEN o.source ILIKE 'etsy' THEN o.quantity ELSE 0 END) `;
      }
      if (fbm) {
        if (a != '') {
          a += ' + ';
        }
        a += `SUM(CASE WHEN o.sales_channel ILIKE 'amazon.com' AND o.fulfillment_channel ILIKE '%mfn%' THEN o.quantity ELSE 0 END)`;
      }

      return a;
    };

    const getTotalOrderQtyQuery = () => {
      var a = '';
      if (shopify) {
        a += `SUM(CASE WHEN o.source ILIKE 'shopify' THEN o.quantity ELSE 0 END)`;
      }
      if (etsy) {
        if (a != '') {
          a += ' + ';
        }
        a += `SUM(CASE WHEN o.source ILIKE 'etsy' THEN o.quantity ELSE 0 END) `;
      }
      if (fbm) {
        if (a != '') {
          a += ' + ';
        }
        a += `SUM(CASE WHEN o.sales_channel ILIKE 'amazon.com' AND o.fulfillment_channel ILIKE '%mfn%' THEN o.quantity ELSE 0 END)`;
      }
      if (fba) {
        if (a != '') {
          a += ' + ';
        }
        a += `SUM(CASE WHEN o.sales_channel ILIKE 'amazon.com' AND o.fulfillment_channel ILIKE '%afn%' THEN o.quantity ELSE 0 END)`;
      }

      return a;
    };

    const getFbaOrderQty = () => {
      var a = '';
      if (fba) {
        a += `SUM(CASE WHEN o.sales_channel ILIKE 'amazon.com' AND o.fulfillment_channel ILIKE '%afn%' THEN o.quantity ELSE 0 END)`;
      }
      return a;
    };

    const totalOrderQtyQuery = getTotalOrderQtyQuery();
    const localOrderQtyQuery = getLocalOrderQty();
    const fbaOrderQtyQuery = getFbaOrderQty();

    if (totalOrderQtyQuery != '') {
      selectFields.push(`(${totalOrderQtyQuery}) AS total_sales`);
    }

    if (localOrderQtyQuery != '') {
      selectFields.push(
        `Round(GREATEST(((CAST(${localOrderQtyQuery} AS DOUBLE PRECISION)) / CAST(${months} AS DOUBLE PRECISION) * ${multiplier}) - p.shopify_quantity, 0)) AS local_order`,
      );
    }
    if (fbaOrderQtyQuery != '') {
      selectFields.push(
        `Round(GREATEST(((CAST(${fbaOrderQtyQuery} AS DOUBLE PRECISION)) / CAST(${months} AS DOUBLE PRECISION) * ${multiplier}) - p.amazon_quantity, 0)) AS amazon_order`,
      );
    }

    if (fbaOrderQtyQuery != '' || localOrderQtyQuery != '') {
      const fbaOrderQty =
        fbaOrderQtyQuery != ''
          ? `Round(GREATEST(((CAST(${fbaOrderQtyQuery} AS DOUBLE PRECISION)) / CAST(${months} AS DOUBLE PRECISION) * ${multiplier}) - p.amazon_quantity, 0))`
          : 0;
      const localOrderQty =
        localOrderQtyQuery != ''
          ? `Round(GREATEST(((CAST(${localOrderQtyQuery} AS DOUBLE PRECISION)) / CAST(${months} AS DOUBLE PRECISION) * ${multiplier}) - p.shopify_quantity, 0))`
          : 0;

      selectFields.push(`${fbaOrderQty} + ${localOrderQty} AS qty_order`);
    }

    // Build the query using TypeORM QueryBuilder
    const salesData = this.orderRepository
      .createQueryBuilder('o')
      .select(selectFields)
      .leftJoin(
        'products',
        'p',
        'p.size = o.size AND p.style = o.style AND p.color = o.color',
      )
      .where(`o.style != ''`)
      .andWhere(`o.color != ''`)
      .andWhere(`p.color != ''`)
      .andWhere(`p.style != ''`)
      .andWhere(`p.sku NOT ILIKE 'ag-%'`)
      .andWhere(`p.sku NOT ILIKE 'g-%'`)
      .andWhere(`p.sku NOT ILIKE '%-FBM'`)
      .andWhere(
        'o.external_created_at >= :date AND o.external_created_at <= :date2',
        { date: formattedStartDate, date2: formattedEndDate },
      )
      .andWhere(`(o.status != 'Canceled' OR o.status IS NULL)`);

    if (PAGE_SIZE > 0) {
      salesData.addSelect(`p.inventory_item_id as inventory_item_id`);
      salesData.addSelect(`p.external_product_id as external_product_id`);
    }

    if (jsonData['query']) {
      const query = `%${jsonData['query']}%`;
      const keywords = query.split(' ');
      salesData.andWhere(
        new Brackets((subQb) => {
          subQb.andWhere([
            { style: ILike(`%${keywords[0]}%`) },
            { color: ILike(`%${keywords[0]}%`) },
            {
              size: isNaN(parseInt(keywords[0]))
                ? -1
                : Equal(parseInt(keywords[0])),
            },
          ]);
          for (let i = 1; i < keywords.length; i++) {
            subQb.andWhere([
              { style: ILike(`%${keywords[i]}%`) },
              {
                size: isNaN(parseInt(keywords[i]))
                  ? -1
                  : Equal(parseInt(keywords[i])),
              },
              { color: ILike(`%${keywords[i]}%`) },
            ]);
          }
        }),
      );
    }

    salesData.groupBy(
      'p.color, p.size, p.style, p.amazon_quantity, p.shopify_quantity, p.inventory_item_id, p.external_product_id, p.asin, p.fnsku, p.sku',
    );

    salesData.orderBy('style', 'DESC').addOrderBy('color', 'DESC');

    const total = await salesData.getRawMany();

    let [rawQuery, params] = salesData.getQueryAndParameters();
    if (PAGE_SIZE > 0) {
      rawQuery += ` LIMIT ${PAGE_SIZE}`;
      rawQuery += ` OFFSET ${(pageNumber - 1) * PAGE_SIZE}`;
    }

    this.logger.debug(`Raw Query: ${rawQuery}`);
    this.logger.debug(`Params: ${JSON.stringify(params)}`);

    const rawResult: SalesDataQuery[] = await this.orderRepository.query(
      rawQuery,
      params,
    );

    const pageCount = Math.ceil(total.length / PAGE_SIZE);
    const response = new PageDto(rawResult, {
      hasNextPage: pageNumber < pageCount,
      hasPreviousPage: pageNumber !== 1,
      itemCount: total.length,
      page: pageNumber,
      take: PAGE_SIZE,
      pageCount,
      params: {
        startDate: formattedStartDate,
        endDate: formattedEndDate,
      },
    });

    if (pageCount > 0) await this.cacheService.set(cacheKey, response, 300);
    return response;
  }

  private async fetchSalesData(
    startDate: string,
    endDate: string,
  ): Promise<any[]> {
    const salesQuery = this.orderRepository
      .createQueryBuilder('o')
      .select([
        'o.style as style',
        'o.size as size',
        'o.color as color',
        `SUM(CASE WHEN o.sales_channel ILIKE 'amazon.com' and o.fulfillment_channel ILIKE '%mfn%' THEN o.quantity ELSE 0 END) AS fbm_sales`,
        `SUM(CASE WHEN o.source ILIKE 'etsy' THEN o.quantity ELSE 0 END) AS etsy_sales`,
        `SUM(CASE WHEN o.source ILIKE 'shopify' AND o.outside IS NOT NULL AND o.outside != '' THEN o.quantity ELSE 0 END) AS shopify_sales`,
        `(SUM(CASE WHEN o.source ILIKE 'shopify' AND o.outside IS NOT NULL AND o.outside != '' THEN o.quantity ELSE 0 END))+(SUM(CASE WHEN o.sales_channel ILIKE 'amazon.com' and o.fulfillment_channel ILIKE '%mfn%' THEN o.quantity ELSE 0 END) + SUM(CASE WHEN o.source ILIKE 'etsy' THEN o.quantity ELSE 0 END)) AS total_sales`,
        'GREATEST(p.shopify_quantity, 0) AS local_inventory',
        'p.inventory_item_id as inventory_item_id',
        'p.external_product_id as external_product_id',
      ])
      .leftJoin(
        'products',
        'p',
        'p.size = o.size AND p."style" = o."style" AND p.color = o.color',
      )
      .where(`o.style != ''`)
      .andWhere(`o.color != ''`)
      .andWhere(`o.saved = false`)
      .andWhere(`p.inventory_item_id != ''`)
      .andWhere('o.external_created_at >= :startDate', { startDate })
      .andWhere('o.external_created_at <= :endDate', { endDate })
      // .andWhere(`(o.status ilike 'shipped' or o.status ilike 'completed')`)
      .andWhere(`(o.status != 'Canceled' or o.status is null)`)
      .groupBy(
        'o.color, o.size, o.style, p.shopify_quantity, p.inventory_item_id, p.external_product_id',
      )
      .having(
        "SUM(CASE WHEN o.sales_channel ILIKE 'amazon.com' and o.fulfillment_channel ILIKE '%mfn%' THEN o.quantity ELSE 0 END) + SUM(CASE WHEN o.source ILIKE 'etsy' THEN o.quantity ELSE 0 END) + SUM(CASE WHEN o.source ILIKE 'shopify' AND o.outside IS NOT NULL AND o.outside != '' THEN o.quantity ELSE 0 END) > 0",
      )
      .orderBy('total_sales', 'DESC');

    const [rawQuery, params] = salesQuery.getQueryAndParameters();

    return this.orderRepository.query(rawQuery, params);
  }

  private transformRawDataToEntities(
    rawData: any[],
    startTime: string,
  ): SoldProductEntity[] {
    return rawData.map((item) => {
      const soldProduct = new SoldProductEntity();
      soldProduct.style = item.style ?? '';
      soldProduct.size = item.size ?? 0;
      soldProduct.color = item.color ?? '';
      soldProduct.fbm_sales = item.fbm_sales ?? 0;
      soldProduct.shopify_sales = item.shopify_sales ?? 0;
      soldProduct.etsy_sales = item.etsy_sales ?? 0;
      soldProduct.total_sales = item.total_sales ?? 0;
      soldProduct.inventory_item_id = item.inventory_item_id ?? '';
      soldProduct.external_product_id = item.external_product_id ?? '';
      soldProduct.salesDate = new Date(startTime);
      return soldProduct;
    });
  }

  private async updateOrdersAsSaved(
    soldProducts: SoldProductEntity[],
    startDate: string,
    endDate: string,
  ): Promise<void> {
    let totalAffectedRows = 0;

    for (const soldProduct of soldProducts) {
      const result = await this.orderRepository
        .createQueryBuilder()
        .update(OrderEntity)
        .set({ saved: true })
        .where(
          'style = :style AND size = :size AND color = :color AND saved = :saved AND external_created_at >= :startDate AND external_created_at <= :endDate AND ' +
            '((source ILIKE :amazonSource AND fulfillment_channel ILIKE :fulfillmentChannel) OR ' +
            'source ILIKE :etsySource OR ' +
            "(source ILIKE :shopifySource AND outside IS NOT NULL AND outside != '')) AND " +
            '(status != :statusCanceled or status is null)',
          {
            style: soldProduct.style,
            size: soldProduct.size,
            color: soldProduct.color,
            startDate,
            endDate,
            amazonSource: 'amazon',
            fulfillmentChannel: '%mfn%',
            etsySource: 'etsy',
            shopifySource: 'shopify',
            statusCanceled: 'canceled',
            saved: false,
          },
        )
        .execute();

      totalAffectedRows += result.affected || 0;
    }

    this.logger.warn(`Total affected rows: ${totalAffectedRows}`);
  }

  @Cron(CronExpression.EVERY_DAY_AT_7AM)
  async updateOrdersDaily() {
    try {
      const oneMonthAgo = subDays(new Date(), 14);
      const batchSize = 500;
      let skip = 0;
      let hasMoreOrders = true;

      while (hasMoreOrders) {
        const orders = await this.orderRepository.find({
          where: {
            externalCreatedAt: MoreThan(oneMonthAgo),
            source: In(['Shopify', 'Etsy']),
          },
          take: batchSize,
          skip: skip,
        });

        if (orders.length === 0) {
          hasMoreOrders = false;
          break;
        }

        this.logger.verbose(`Processing batch of orders: ${orders.length}`);
        await this.processOrders(orders);
        skip += batchSize;
      }
      await this.getOrdersWithMatchedColors();
      await this.getOrdersWithMatchedDesigns();
    } catch (error) {
      this.logger.error('Error updating orders daily:', error);
    }
  }

  private async processOrders(orders: OrderEntity[]) {
    const orderIds = orders.map((order) => this.formatOrderId(order));
    const params = { itemIds: orderIds.join(',') };
    const response = await this.axiosHandler.get('items', { params });
    const responseData = response.data.items;

    for (let index = 0; index < orders.length; index++) {
      const order = orders[index];
      const formattedOrderId = this.formatOrderId(order);
      const responseItem = responseData.find(
        (item) => item.itemId === formattedOrderId,
      );

      if (responseItem) {
        let changes: string[] = [];

        if ('notes' in responseItem) {
          this.updateOrderField(order, 'note', responseItem.notes, changes);
        }

        if ('outside' in responseItem) {
          if ('design' in responseItem.outside) {
            this.updateOrderField(
              order,
              'outside',
              responseItem.outside.design,
              changes,
            );
          }
          if ('text' in responseItem.outside) {
            this.updateOrderField(
              order,
              'outside',
              responseItem.outside.text,
              changes,
            );
          }
        }
        if ('inside' in responseItem) {
          if ('design' in responseItem.inside) {
            this.updateOrderField(
              order,
              'inside',
              responseItem.inside.design,
              changes,
            );
          }
          if ('text' in responseItem.inside) {
            this.updateOrderField(
              order,
              'inside',
              responseItem.inside.text,
              changes,
            );
          }
        }

        if ('size' in responseItem) {
          const sizeInt = parseInt(responseItem.size.replace(/\D/g, ''), 10);
          this.updateOrderField(order, 'size', sizeInt, changes);
        }

        if (changes.length > 0) {
          await this.orderRepository.save(order);
          this.logger.warn(
            `Order ID ${order.order_id} updated with changes: ${changes.join(', ')}`,
          );
        }
      }
    }
  }

  private formatOrderId(order: any): string {
    let index = Number.parseInt(order.item_no.split('/')[0]) - 1;
    if (isNaN(index)) {
      index = 0;
    }
    return `${order.order_id.replace('#', '')}.${index}`;
  }

  private updateOrderField(
    order: any,
    field: string,
    newValue: any,
    changes: string[],
  ) {
    try {
      if (
        newValue !== undefined &&
        newValue !== null &&
        order[field] !== newValue
      ) {
        changes.push(`${field}: ${order[field]} -> ${newValue}`);
        order[field] = newValue;
      }
    } catch (error) {
      this.logger.error('Error updating order field:', error);
    }
  }

  @Cron(CronExpression.EVERY_DAY_AT_7AM)
  async getOrdersLast24Hours() {
    await this.getOrdersWithMatchedColors();
    await this.getOrdersWithMatchedDesigns();

    let today = new Date();

    if (this.appConfig.isDevelopment) {
      today = subDays(new Date(), 1);
    }

    const startTime = startOfDay(today);

    const formattedStartTime = convertToSQLDateTime(
      startTime,
      false,
      this.appConfig.isDevelopment,
    );
    const formattedEndTime = convertToSQLDateTime(
      startTime,
      true,
      this.appConfig.isDevelopment,
    );

    const salesData = await this.fetchSalesData(
      formattedStartTime,
      formattedEndTime,
    );

    const soldProducts = this.transformRawDataToEntities(
      salesData,
      formattedEndTime,
    );

    await this.soldProductRepository.save(soldProducts);

    await this.updateOrdersAsSaved(
      soldProducts,
      formattedStartTime,
      formattedEndTime,
    );
    this.logger.warn(`soldProducts: ${soldProducts.length}`);
  }

  async getMostSalesByRingColorSize(
    startDate: Date,
    endDate: Date,
  ): Promise<any> {
    const sales = await this.orderRepository
      .createQueryBuilder('o')
      .select([
        'o.style as style',
        'o.color as color',
        'o.size as size',
        `SUM(o.quantity) AS qty`,
        `SUM(o.quantity * o.price) AS total`,
      ])
      .where('o.external_created_at BETWEEN :startDate AND :endDate', {
        startDate,
        endDate,
      })
      .andWhere(`o.style != ''`)
      .andWhere(`o.color != ''`)
      .andWhere('(o.status IS NULL OR o.status != :canceled)', {
        canceled: 'Canceled',
      })
      .groupBy('o.style')
      .addGroupBy('o.color')
      .addGroupBy('o.size')
      .orderBy('qty', 'DESC')
      .getRawMany();

    return sales;
  }

  async getSalesVelocity(data?: string) {
    const jsonData = '';
    const cacheKey = JSON.stringify(`Sales_Velocity_${data}`);

    const cachedData: any = await this.cacheService.get(cacheKey);
    if (cachedData) {
      this.logger.debug('cachedData');
      return cachedData;
    }

    const currentDate = new Date();
    // Deduct 90 days
    const subtractedDate90 = new Date(currentDate);
    subtractedDate90.setDate(currentDate.getDate() - 90);

    const formattedStartDate = convertToSQLDateTime(
      subtractedDate90,
      false,
      this.appConfig.isDevelopment,
    );

    const salesData = this.orderRepository
      .createQueryBuilder('o')
      .select([
        'o.style as style',
        'o.color as color',
        'o.size as size',
        `SUM(o.quantity) AS total_sales`,
      ])
      .leftJoin(
        'products',
        'p',
        'p.size = o.size AND p."style" = o."style" AND p.color = o.color',
      )
      .where(`o.style != ''`)
      .andWhere(`o.color != ''`)
      .andWhere(`(p.design = '' or p.design is null)`)
      .andWhere('o.external_created_at >= :date', { date: formattedStartDate })
      .andWhere(`(o.status != 'Canceled' or o.status is null)`);

    if (jsonData['query']) {
      const query = `%${jsonData['query']}%`;
      const keywords = query.split(' ');
      salesData.andWhere(
        new Brackets((subQb) => {
          subQb.andWhere([
            { style: ILike(`%${keywords[0]}%`) },
            { color: ILike(`%${keywords[0]}%`) },
            {
              size: isNaN(parseInt(keywords[0]))
                ? -1
                : Equal(parseInt(keywords[0])),
            },
          ]);
          for (let i = 1; i < keywords.length; i++) {
            subQb.andWhere([
              { style: ILike(`%${keywords[i]}%`) },

              { color: ILike(`%${keywords[i]}%`) },
              {
                size: isNaN(parseInt(keywords[i]))
                  ? -1
                  : Equal(parseInt(keywords[i])),
              },
            ]);
          }
        }),
      );
    }

    salesData.groupBy('o.color, o.size, o.style');
    salesData.orderBy('style').addOrderBy('color');

    let [rawQuery, params] = salesData.getQueryAndParameters();

    const rawResult: SalesVelocityDataQuery[] =
      await this.orderRepository.query(rawQuery, params);

    const salesVelocityData: {
      style: string | undefined;
      color: string | undefined;
      size: string | undefined;
      totalSales: string | undefined;
      lastOrderDate: Date | undefined;
      salesVelocity: number;
    }[] = [];

    for (let index = 0; index < rawResult.length; index++) {
      const element = rawResult[index];

      const lastOrder = await this.orderRepository
        .createQueryBuilder('order')
        .select('MAX(order.external_created_at)', 'lastOrderDate')
        .where('order.style = :style', { style: element.style })
        .andWhere('order.color = :color', { color: element.color })
        .andWhere('order.size = :size', { size: element.size })
        .getRawOne();

      const lastOrderDate = lastOrder.lastOrderDate;

      // Step 3: Get total sales for 3 months before the last order date
      const threeMonthsAgo = new Date();
      threeMonthsAgo.setMonth(threeMonthsAgo.getMonth() - 3);

      const totalSalesQuery = await this.orderRepository
        .createQueryBuilder('order')
        .select('SUM(order.quantity)', 'totalSales')
        .where('order.style = :style', { style: element.style })
        .andWhere('order.color = :color', { color: element.color })
        .andWhere('order.size = :size', { size: element.size })
        .andWhere('order.external_created_at >= :startDate', {
          startDate: threeMonthsAgo,
        })
        .andWhere('order.external_created_at <= :endDate', {
          endDate: lastOrderDate,
        })
        .getRawOne();

      const salesVelocity = +(totalSalesQuery.totalSales / 3).toFixed(2);

      salesVelocityData.push({
        style: element.style,
        color: element.color,
        size: element.size,
        lastOrderDate: lastOrderDate,
        totalSales: totalSalesQuery.totalSales ?? 0,
        salesVelocity,
      });
    }

    salesVelocityData.sort((a, b) => b.salesVelocity - a.salesVelocity);
    await this.cacheService.set(cacheKey, salesVelocityData, 3.6e6);

    return salesVelocityData;
  }

  async getEngravedSalesInfo(
    paginateOptions: PaginateProductsOptionsDto,
    data?: string,
  ) {
    const jsonData = JSON.parse(data ?? '');
    const PAGE_SIZE = paginateOptions.take;
    const pageNumber = paginateOptions.page;
    const months = jsonData.dateRange;
    const multiplier = months == 3 ? 2 : months == 6 ? 1 : 6;

    const { query, startDate, endDate } = jsonData;

    const cacheKey = JSON.stringify(
      `Engraved_Sales_Info_${JSON.stringify(paginateOptions)}/${data}`,
    );

    const cachedData: any = await this.cacheService.get(cacheKey);
    if (cachedData) {
      this.logger.debug('cachedData');
      return cachedData;
    }

    const currentDate = new Date();
    // Deduct 90 days
    const subtractedDate90 = new Date(currentDate);
    subtractedDate90.setDate(currentDate.getDate() - 90);

    // Deduct 180 days
    const subtractedDate180 = new Date(currentDate);
    subtractedDate180.setDate(currentDate.getDate() - 180);

    const formattedStartDate = convertToSQLDateTime(
      months == 3 ? subtractedDate90 : subtractedDate180,
      false,
      this.appConfig.isDevelopment,
    );

    const salesData = this.orderRepository
      .createQueryBuilder('o')
      .select([
        'o.style as style',
        'o.size as size',
        'o.color as color',
        'pd.title AS design',
        'pd.quantity AS fba_inventory',
        `SUM(CASE WHEN o.sales_channel ILIKE 'amazon.com' and o.fulfillment_channel ILIKE '%afn%' THEN o.quantity ELSE 0 END) AS fba_sales`,
        `SUM(CASE WHEN o.sales_channel ILIKE 'amazon.com' THEN o.quantity ELSE 0 END) AS total_sales`,
        `GREATEST((SUM(CASE WHEN o.sales_channel ILIKE 'amazon.com' and o.fulfillment_channel ILIKE '%afn%' THEN o.quantity ELSE 0 END) * ${multiplier} - pd.quantity), 0) AS fba_order`,
        `SUM(CASE WHEN o.sales_channel ILIKE 'amazon.com' and o.fulfillment_channel ILIKE '%mfn%' THEN o.quantity ELSE 0 END) AS fbm_sales`,
      ])
      .leftJoin(
        'products',
        'p',
        'p.size = o.size AND p."style" = o."style" AND p.color = o.color',
      )
      .leftJoin(
        'product_designs',
        'pd',
        'pd.product_id = p.id and o.outside = pd.title',
      )
      .where(`o.style != ''`)
      .where(`pd.title != ''`)
      .andWhere('o.external_created_at >= :date', { date: formattedStartDate })
      .andWhere(`(o.status != 'Canceled' or o.status is null)`);

    if (startDate && endDate) {
    }

    if (query) {
      const searchKeys = `%${query}%`;
      const keywords = searchKeys.split(' ');
      salesData.andWhere(
        new Brackets((subQb) => {
          subQb.andWhere([
            { style: ILike(`%${keywords[0]}%`) },
            { color: ILike(`%${keywords[0]}%`) },
            { sku: ILike(`%${keywords[0]}%`) },
            { outside: ILike(`%${keywords[0]}%`) },
            {
              size: isNaN(parseInt(keywords[0]))
                ? -1
                : Equal(parseInt(keywords[0])),
            },
          ]);
          for (let i = 1; i < keywords.length; i++) {
            subQb.andWhere([
              { style: ILike(`%${keywords[i]}%`) },
              { color: ILike(`%${keywords[i]}%`) },
              { sku: ILike(`%${keywords[i]}%`) },
              { outside: ILike(`%${keywords[i]}%`) },
              {
                size: isNaN(parseInt(keywords[i]))
                  ? -1
                  : Equal(parseInt(keywords[i])),
              },
            ]);
          }
        }),
      );
    }

    salesData.groupBy(
      'o.color, o.size, o.style, o.outside,pd.title,pd.quantity',
    );
    salesData
      .orderBy('pd.title')
      .addOrderBy('o.style')
      .addOrderBy('o.color')
      .addOrderBy('o.size');

    const total = await salesData.getRawMany();

    let [rawQuery, params] = salesData.getQueryAndParameters();
    if (PAGE_SIZE > 0) {
      rawQuery += ` LIMIT ${PAGE_SIZE}`;
      rawQuery += ` OFFSET ${(pageNumber - 1) * PAGE_SIZE}`;
    }

    const rawResult: SalesDataQuery[] = await this.orderRepository.query(
      rawQuery,
      params,
    );
    const pageCount = Math.ceil(total.length / PAGE_SIZE);

    const response = new PageDto(rawResult, {
      hasNextPage: pageNumber < pageCount,
      hasPreviousPage: pageNumber == 1 ? false : true,
      itemCount: total.length,
      page: pageNumber,
      take: PAGE_SIZE,
      pageCount: pageCount,
    });

    if (pageCount > 0) await this.cacheService.set(cacheKey, response, 300);
    return response;
  }

  async topItems(data?: string) {
    const topRings = await (await this.getQueryBuilder(data))
      .select('SUM(quantity) as sumQuantity, sku')
      .groupBy('sku')
      .orderBy('sumQuantity', 'DESC')
      .limit(15)
      .getRawMany();

    const topStyle = await (await this.getQueryBuilder(data))
      .select('SUM(quantity) as sumQuantity, style')
      .groupBy('style')
      .orderBy('sumQuantity', 'DESC')
      .limit(15)
      .getRawMany();

    const topSize = await (await this.getQueryBuilder(data))
      .select('SUM(quantity) as sumQuantity, size')
      .groupBy('size')
      .orderBy('sumQuantity', 'DESC')
      .limit(15)
      .getRawMany();

    const topColor = await (await this.getQueryBuilder(data))
      .select('SUM(quantity) as sumQuantity, color')
      .groupBy('color')
      .orderBy('sumQuantity', 'DESC')
      .limit(15)
      .getRawMany();

    const topDesign = await (await this.getQueryBuilder(data))
      .select('SUM(quantity) as sumQuantity, outside')
      .groupBy('outside')
      .orderBy('sumQuantity', 'DESC')
      .limit(15)
      .getRawMany();

    return {
      topRings: topRings,
      topStyle: topStyle,
      topSize: topSize,
      topColor: topColor,
      topDesign: topDesign,
    };
  }

  async getQueryBuilder(data?: string) {
    const queryBuilder = this.orderRepository.createQueryBuilder('order');
    if (data) {
      try {
        const jsonData = JSON.parse(data);

        const {
          query,
          shopify,
          amazonFBA,
          amazonFBM,
          etsy,
          includeEngraving,
          mustContainAllKeywords,
          startDate,
          endDate,
        } = jsonData;

        if (startDate && endDate) {
          const startTime = convertToSQLDateTime(
            startDate,
            false,
            this.appConfig.isDevelopment,
          );
          const endTime = convertToSQLDateTime(
            endDate,
            true,
            this.appConfig.isDevelopment,
          );
          queryBuilder.where(
            `order.externalCreatedAt BETWEEN '${startTime}' AND '${endTime}'`,
          );
        } else if (startDate) {
          const startTime = convertToSQLDateTime(
            startDate,
            false,
            this.appConfig.isDevelopment,
          );
          queryBuilder.where(`order.externalCreatedAt >= '${startTime}'`);
        } else if (endDate) {
          const endTime = convertToSQLDateTime(
            endDate,
            true,
            this.appConfig.isDevelopment,
          );
          queryBuilder.where(`order.externalCreatedAt <= '${endTime}'`);
        }

        // Check if any of the sources is true
        if (shopify || amazonFBA || amazonFBM || etsy) {
          const sourceConditions: any = [];

          if (shopify) {
            sourceConditions.push(`LOWER(order.source) LIKE '%shopify%'`);
          }
          if (amazonFBA) {
            sourceConditions.push(
              `order.sales_channel ILIKE 'amazon.com' AND order.fulfillmentChannel ILIKE '%afn%'`,
            );
          }
          if (amazonFBM) {
            sourceConditions.push(
              `order.sales_channel ILIKE 'amazon.com' AND order.fulfillmentChannel ILIKE '%mfn%'`,
            );
          }

          if (etsy) sourceConditions.push(`LOWER(order.source) LIKE '%etsy%'`);

          if (sourceConditions.length > 0) {
            queryBuilder.andWhere(
              new Brackets((qb) => {
                sourceConditions.forEach((condition, index) => {
                  if (index === 0) {
                    qb.andWhere(condition);
                  } else {
                    qb.orWhere(condition);
                  }
                });
              }),
            );
          }
        }
        // Check if engraving is not included
        if (!includeEngraving) {
          queryBuilder.andWhere(
            new Brackets((qb) => {
              qb.where(`COALESCE(order.outside, '') = ''`).andWhere(
                `COALESCE(order.inside, '') = ''`,
              );
            }),
          );
        }

        // Check if query is provided
        if (query) {
          const keywords = query.split(' ');
          if (mustContainAllKeywords) {
            queryBuilder.andWhere(
              new Brackets((subQb) => {
                subQb.andWhere([
                  {
                    style: ILike(this.getConditionKeyword(keywords[0])),
                  },
                  {
                    outside: ILike(this.getConditionKeyword(keywords[0])),
                  },
                  { sku: ILike(this.getConditionKeyword(keywords[0])) },
                  {
                    color: ILike(this.getConditionKeyword(keywords[0])),
                  },
                  {
                    size: isNaN(parseInt(keywords[0]))
                      ? -1
                      : Equal(parseInt(keywords[0])),
                  },
                ]);

                for (let i = 1; i < keywords.length; i++) {
                  subQb.andWhere([
                    { style: ILike(this.getConditionKeyword(keywords[i])) },
                    { outside: ILike(this.getConditionKeyword(keywords[i])) },
                    { sku: ILike(this.getConditionKeyword(keywords[i])) },
                    { color: ILike(this.getConditionKeyword(keywords[i])) },
                    {
                      size: isNaN(parseInt(keywords[i]))
                        ? -1
                        : Equal(parseInt(keywords[i])),
                    },
                  ]);
                }
              }),
            );
          } else {
            queryBuilder.andWhere(
              new Brackets((subQb) => {
                subQb.orWhere([
                  { style: ILike(this.getConditionKeyword(keywords[0])) },
                  { outside: ILike(this.getConditionKeyword(keywords[0])) },
                  { sku: ILike(this.getConditionKeyword(keywords[0])) },
                  { color: ILike(this.getConditionKeyword(keywords[0])) },
                  {
                    size: isNaN(parseInt(keywords[0]))
                      ? -1
                      : Equal(parseInt(keywords[0])),
                  },
                ]);

                for (let i = 1; i < keywords.length; i++) {
                  subQb.orWhere([
                    { style: ILike(this.getConditionKeyword(keywords[i])) },
                    { outside: ILike(this.getConditionKeyword(keywords[i])) },
                    { sku: ILike(this.getConditionKeyword(keywords[i])) },
                    { color: ILike(this.getConditionKeyword(keywords[i])) },
                    {
                      size: isNaN(parseInt(keywords[i]))
                        ? -1
                        : Equal(parseInt(keywords[i])),
                    },
                  ]);
                }
              }),
            );
          }
        }
      } catch (error) {
        console.error('Error parsing JSON:', error);
      }
    }

    queryBuilder.andWhere(
      `(order.status IS NULL OR order.status != 'Canceled')`,
    );

    return queryBuilder;
  }

  getConditionKeyword(keyword: string) {
    if (keyword.startsWith('"') && keyword.endsWith('"')) {
      return keyword.slice(1, -1);
    } else {
      return `%${keyword}%`;
    }
  }

  async getMinifiedOrders(
    paginateOptions: PaginateOrdersOptionsDto,
  ): Promise<PageDto<OrderDto>> {
    const queryBuilder = this.orderRepository.createQueryBuilder('order');
    queryBuilder
      .select('order_id')
      .addSelect('customer_name')
      .addSelect('email')
      .addSelect('source')
      .addSelect(
        "TO_CHAR(order.externalCreatedAt, 'YYYY-MM-DD HH24:MI:SS') as createdAt",
      )
      .addSelect('Count(*)', 'quantity')
      .addGroupBy('order_id')
      .addGroupBy('customer_name')
      .addGroupBy('createdAt')
      .addGroupBy('source')
      .addGroupBy('email')
      .addGroupBy('quantity')
      .orderBy('createdAt', 'DESC');

    queryBuilder.andWhere(
      `(order.status IS NULL OR order.status != 'Canceled')`,
    );
    const [items, pageMetaDto] =
      await queryBuilder.paginateRaw(paginateOptions);

    return new PageDto(items, pageMetaDto);
  }

  async topProducts(
    paginateOptions: PaginateOrdersOptionsDto,
    basedOn: string,
  ): Promise<PageDto<OrderDto>> {
    const queryBuilder = this.orderRepository.createQueryBuilder('order');
    let selectedFilter = [];

    if (basedOn) {
      try {
        selectedFilter = JSON.parse(basedOn);
      } catch (error) {
        console.error('Error parsing searchQuery:', error);
      }
    }

    let str: string[] = [];
    queryBuilder.select('SUM(quantity)', 'quantity');

    selectedFilter.forEach((e) => {
      queryBuilder.addSelect(`${e}`);
      queryBuilder.addGroupBy(`${e}`);
      str.push(e);
    });

    queryBuilder.orderBy('quantity', 'DESC');

    const customQuery = paginateOptions.searchQuery || '';
    let searchQueryObject = new SearchQuery();

    if (customQuery) {
      try {
        searchQueryObject = JSON.parse(customQuery);
      } catch (error) {
        console.error('Error parsing searchQuery:', error);
      }
    }

    const { minDate, maxDate } = searchQueryObject;

    if (minDate && maxDate) {
      const startTime = convertToSQLDateTime(
        minDate,
        false,
        this.appConfig.isDevelopment,
      );
      const endTime = convertToSQLDateTime(
        maxDate,
        true,
        this.appConfig.isDevelopment,
      );
      queryBuilder.where(
        `order.externalCreatedAt BETWEEN '${startTime}' AND '${endTime}'`,
      );
    } else if (minDate) {
      const startTime = convertToSQLDateTime(
        minDate,
        false,
        this.appConfig.isDevelopment,
      );
      queryBuilder.where(`order.externalCreatedAt >= '${startTime}'`);
    } else if (maxDate) {
      const endTime = convertToSQLDateTime(
        maxDate,
        true,
        this.appConfig.isDevelopment,
      );
      queryBuilder.where(`order.externalCreatedAt <= '${endTime}'`);
    }

    queryBuilder.andWhere(
      `(order.status IS NULL OR order.status != 'Canceled')`,
    );
    const [items, pageMetaDto] =
      await queryBuilder.paginateRaw(paginateOptions);

    return new PageDto(items, pageMetaDto);
  }

  async byPlatform(data: string, dateOption?: string): Promise<any> {
    const jsonData = JSON.parse(data);
    const countPerDates: CountPerDate[] = [];

    for (let index = 0; index < jsonData.length; index++) {
      const { start, end } = jsonData[index];
      const startTime = convertToSQLDateTime(
        start,
        false,
        this.appConfig.isDevelopment,
      );
      const endTime = convertToSQLDateTime(
        end,
        true,
        this.appConfig.isDevelopment,
      );

      const queryBuilder = this.orderRepository.createQueryBuilder('order');

      queryBuilder.select([
        "SUM(CASE WHEN LOWER(source) = 'shopify' THEN order.current_subtotal_price ELSE 0 END) as shopify",
        "SUM(CASE WHEN sales_channel ILIKE 'amazon.com' THEN order.current_subtotal_price ELSE 0 END) as amazon",
        "SUM(CASE WHEN sales_channel ILIKE 'amazon.com' AND fulfillment_channel ILIKE 'mfn' THEN order.current_subtotal_price ELSE 0 END) as fbm",
        "SUM(CASE WHEN sales_channel ILIKE 'amazon.com' AND fulfillment_channel ILIKE 'afn' THEN order.current_subtotal_price ELSE 0 END) as fba",
        "SUM(CASE WHEN LOWER(source) = 'etsy' THEN order.current_subtotal_price ELSE 0 END) as etsy",
        "SUM(CASE WHEN LOWER(source) NOT IN ('shopify', 'amazon', 'etsy') THEN order.current_subtotal_price ELSE 0 END) as others",
        'SUM(order.current_subtotal_price) as total',
      ]);
      queryBuilder.andWhere(
        `(order.status IS NULL OR order.status != 'Canceled')`,
      );
      queryBuilder.where(
        `order.externalCreatedAt BETWEEN '${startTime}' AND '${endTime}'`,
      );

      const countryStats = await queryBuilder.getRawOne();

      countPerDates.push({
        label:
          dateOption == 'weekly'
            ? `${formatDate(new Date(startTime))} to ${formatDate(
                new Date(endTime),
              )}`
            : dateOption == 'daily'
              ? `${formatDate(new Date(endTime))}`
              : `${new Date(endTime).getFullYear()}/${
                  new Date(endTime).getMonth() + 1
                }`,
        startDate: startTime,
        endDate: endTime,
        shopify: countryStats.shopify,
        etsy: countryStats.etsy,
        fbm: countryStats.fbm,
        fba: countryStats.fba,
        total: countryStats.total,
        amazon: countryStats.amazon,
      });
    }

    return countPerDates;
  }

  async byRegion(data?: string): Promise<{
    cityStats: any[];
    provinceStats: any[];
    countryStats: any[];
    totalOrders: number;
  }> {
    const cityStats = await (await this.getQueryBuilder(data))
      .select(['COUNT(DISTINCT order_id) as qty', 'city'])
      .where("city IS NOT NULL AND city <> ''")
      .groupBy('city')
      .orderBy('qty', 'DESC')
      .getRawMany();

    const provinceStats = await (await this.getQueryBuilder(data))
      .select(['COUNT(DISTINCT order_id) as qty', 'province'])
      .where("province IS NOT NULL AND province <> ''")
      .groupBy('province')
      .orderBy('qty', 'DESC')
      .getRawMany();

    const countryStats = await (await this.getQueryBuilder(data))
      .select(['COUNT(DISTINCT order_id) as qty', 'country'])
      .where("country IS NOT NULL AND country <> ''")
      .groupBy('country')
      .orderBy('qty', 'DESC')
      .getRawMany();

    const totalOrders = await (await this.getQueryBuilder(data))
      .select('SUM(quantity) as total')
      .getRawOne();

    return {
      cityStats,
      provinceStats,
      countryStats,
      totalOrders: totalOrders.total,
    };
  }

  async byDesign(data?: string) {
    const designList: string[] = (await this.designRepository.find()).map(
      (design) => design.design,
    );

    const designNameList: string[] = (
      await this.designNameRepository.find()
    ).map((design) => design.value);

    const combinedSet = new Set([...designList, ...designNameList]);
    const combinedList: string[] = Array.from(combinedSet);

    let preengravedQuery = (await this.getQueryBuilder(data))
      .select(['SUM(quantity) as qty'])
      .andWhere(`order.outside IN (:...designList)`, {
        designList: combinedList,
      })
      .andWhere(`COALESCE(order.inside, '') = ''`);

    let preengravedAndInsideQuery = (await this.getQueryBuilder(data))
      .select(['SUM(quantity) as qty'])
      .andWhere(`order.outside IN (:...designList)`, {
        designList: combinedList,
      })
      .andWhere(`COALESCE(order.inside, '') != ''`);

    let outsideQuery = (await this.getQueryBuilder(data))
      .select(['SUM(quantity) as qty'])
      .andWhere(`COALESCE(order.outside, '') != ''`)
      .andWhere(`COALESCE(order.inside, '') = ''`)
      .andWhere(`order.outside NOT IN (:...designList)`, {
        designList: combinedList,
      });

    let insideQuery = (await this.getQueryBuilder(data))
      .select(['SUM(quantity) as qty'])
      .andWhere(`COALESCE(order.outside, '') = ''`)
      .andWhere(`COALESCE(order.inside, '') != ''`);

    let bothQuery = (await this.getQueryBuilder(data))
      .select(['SUM(quantity) as qty'])
      .andWhere(`COALESCE(order.outside, '') != ''`)
      .andWhere(`COALESCE(order.inside, '') != ''`)
      .andWhere(`order.outside not IN (:...designList)`, {
        designList: combinedList,
      });

    let noneQuery = (await this.getQueryBuilder(data))
      .select(['SUM(quantity) as qty'])
      .andWhere(`COALESCE(order.outside, '') = ''`)
      .andWhere(`COALESCE(order.inside, '') = ''`);

    const preengraved = await preengravedQuery.getRawOne();
    const preengravedAndInside = await preengravedAndInsideQuery.getRawOne();
    const outside = await outsideQuery.getRawOne();
    const inside = await insideQuery.getRawOne();
    const both = await bothQuery.getRawOne();
    const none = await noneQuery.getRawOne();

    return { outside, inside, both, none, preengraved, preengravedAndInside };
  }

  @Cron(CronExpression.EVERY_DAY_AT_MIDNIGHT)
  async updateMissingRings(): Promise<void> {
    this.logger.log('Starting updateMissingRings process');
    try {
      const oneMonthAgo = subDays(new Date(), 2);

      const items = await this.orderRepository
        .createQueryBuilder('order')
        .where('order.style = :emptyString', { emptyString: '' })
        .andWhere('order.externalCreatedAt > :oneMonthAgo', { oneMonthAgo })
        .orWhere('(order.color IS NULL OR order.color ILIKE :emptyString)', {
          emptyString: '',
        })
        .andWhere('order.externalCreatedAt > :oneMonthAgo', { oneMonthAgo })
        .orWhere('(order.style IS NULL OR order.style ILIKE :emptyString)', {
          emptyString: '',
        })
        .andWhere('order.externalCreatedAt > :oneMonthAgo', { oneMonthAgo })
        .getMany();

      this.logger.log(`Found ${items.length} items with missing attributes`);

      for (const item of items) {
        try {
          this.logger.log(
            `Processing item with SKU: ${item.sku}  [${item.style}-${item.color}-${item.size}-${item.outside}]`,
          );
          const itemSpecification: RingInfo = await this.skuService.extractSku(
            item.sku ?? '',
          );

          let changes: string[] = [];

          if (item.outside == '' && item.outside != itemSpecification.design) {
            item.outside = itemSpecification.design;
            changes.push(`outside: ${itemSpecification.design}`);
          }

          if (
            (item.color == '' || item.color == null) &&
            item.color != itemSpecification.color
          ) {
            item.color = itemSpecification.color;
            changes.push(`color: ${itemSpecification.color}`);
          }

          if (
            (item.size == 0 || item.size == null) &&
            item.size != itemSpecification.size
          ) {
            item.size = itemSpecification.size;
            changes.push(`size: ${itemSpecification.size}`);
          }

          if (
            (item.style == '' || item.style == null) &&
            item.style != itemSpecification.style
          ) {
            item.style = itemSpecification.style;
            changes.push(`style: ${itemSpecification.style}`);
          }

          if (changes.length > 0) {
            await this.orderRepository.save(item);
            this.logger.warn(
              `Updated item with SKU: ${item.sku} - Changes: ${changes.join(
                ', ',
              )}`,
            );
          }
        } catch (error) {
          this.logger.error(
            `Error processing item with SKU: ${item.sku} - Error: ${error}`,
          );
        }
      }
      this.logger.log('Completed updateMissingRings process');
    } catch (error) {
      this.logger.error(
        `Failed during updateMissingRings process - Error: ${error}`,
      );
    }
  }

  async getOrderDetails(orderId: string): Promise<OrderDto | undefined> {
    try {
      const order = await this.orderRepository
        .createQueryBuilder('order')
        .leftJoinAndSelect('order.activities', 'activities')
        .where('order.id = :orderId', { orderId })
        .orderBy('activities.created_at', 'DESC')
        .getOne();

      return order?.toDto();
    } catch (error) {
      // Handle any errors, log them, etc.
      console.error('Error fetching order details:', error);

      return undefined;
    }
  }

  async getOrderItems(orderId: string): Promise<OrderDto[]> {
    try {
      const order = await this.orderRepository
        .createQueryBuilder('order')
        .where('order.order_id = :orderId', { orderId })
        .getMany();

      // Check if order is not empty before calling toDto()
      return order.length > 0 ? order.map((o) => o.toDto()) : [];
    } catch (error) {
      // Handle any errors, log them, etc.
      console.error('Error fetching order details:', error);

      return [];
    }
  }
  async getMonthlySales(
    paginateOptions: PaginateOrdersOptionsDto,
    param?: string,
  ): Promise<any> {
    const queryBuilder = this.orderRepository
      .createQueryBuilder('order')
      .select([
        'order.style AS style',
        'order.color AS color',
        'order.size AS size',
        'order.outside AS outside',
        "SUM(CASE WHEN DATE_TRUNC('month', order.external_created_at) = DATE_TRUNC('month', CURRENT_DATE) THEN 1 ELSE 0 END) AS current_month",
        "SUM(CASE WHEN DATE_TRUNC('month', order.external_created_at) = DATE_TRUNC('month', CURRENT_DATE) - INTERVAL '1 month' THEN 1 ELSE 0 END) AS one_month_ago",
        "SUM(CASE WHEN DATE_TRUNC('month', order.external_created_at) = DATE_TRUNC('month', CURRENT_DATE) - INTERVAL '2 months' THEN 1 ELSE 0 END) AS two_months_ago",
        "SUM(CASE WHEN DATE_TRUNC('month', order.external_created_at) = DATE_TRUNC('month', CURRENT_DATE) - INTERVAL '3 months' THEN 1 ELSE 0 END) AS three_months_ago",
        "SUM(CASE WHEN DATE_TRUNC('month', order.external_created_at) = DATE_TRUNC('month', CURRENT_DATE) - INTERVAL '4 months' THEN 1 ELSE 0 END) AS four_months_ago",
        "SUM(CASE WHEN DATE_TRUNC('month', order.external_created_at) = DATE_TRUNC('month', CURRENT_DATE) - INTERVAL '5 months' THEN 1 ELSE 0 END) AS five_months_ago",
        "SUM(CASE WHEN DATE_TRUNC('month', order.external_created_at) >= DATE_TRUNC('month', CURRENT_DATE) - INTERVAL '5 months' THEN 1 ELSE 0 END) AS total_6_months",
        'product.shopify_quantity AS shopify_quantity',
        'product.amazon_quantity AS amazon_quantity',
        'product.external_product_id AS external_product_id',
      ])
      .leftJoin(
        'products',
        'product',
        'product.style = order.style AND product.color = order.color AND product.size = order.size',
      )
      .where("order.outside IS NOT NULL AND order.outside <> ''")
      .andWhere("order.style IS NOT NULL AND order.style <> ''")
      .andWhere("order.color IS NOT NULL AND order.color <> ''")
      .andWhere('order.size IS NOT NULL')
      .andWhere("order.status IS NOT NULL AND order.status <> 'canceled'")
      .andWhere(
        "order.external_created_at >= DATE_TRUNC('month', CURRENT_DATE) - INTERVAL '5 months'",
      )
      .groupBy(
        'order.style, order.color, order.size, order.outside, product.shopify_quantity,product.amazon_quantity,product.external_product_id',
      );

    if (param) {
      const jsonData = JSON.parse(param);
      const { query } = jsonData;

      if (query) {
        const keywords = query
          .split(' ')
          .filter((keyword) => keyword.trim() !== '');
        if (keywords.length > 0) {
          queryBuilder.andWhere(
            new Brackets((subQb) => {
              keywords.forEach((keyword) => {
                subQb.andWhere([
                  { style: ILike(this.getConditionKeyword(keyword)) },
                  { outside: ILike(this.getConditionKeyword(keyword)) },
                  { sku: ILike(this.getConditionKeyword(keyword)) },
                  { color: ILike(this.getConditionKeyword(keyword)) },
                  {
                    size: isNaN(parseInt(keyword))
                      ? -1
                      : Equal(parseInt(keyword)),
                  },
                ]);
              });
            }),
          );
        }
      }
    }

    const { page = 1, take = 50 } = paginateOptions || {};
    const sortKey = paginateOptions.orderBy ?? 'total_6_months';
    const sortOrder = paginateOptions.order;

    if (sortKey && sortOrder) {
      queryBuilder.orderBy(sortKey, sortOrder.toUpperCase() as 'ASC' | 'DESC');
    }

    const total = await queryBuilder.getRawMany();
    let [rawQuery, params] = queryBuilder.getQueryAndParameters();
    if (take > 0) {
      rawQuery += ` LIMIT ${take}`;
      rawQuery += ` OFFSET ${(page - 1) * take}`;
    }

    const rawResult = await this.orderRepository.query(rawQuery, params);

    const pageCount = Math.ceil(total.length / take);
    const response = new PageDto(rawResult, {
      hasNextPage: page < pageCount,
      hasPreviousPage: page == 1 ? false : true,
      itemCount: total.length,
      page: page,
      take: take,
      pageCount: pageCount,
    });

    return response;
  }
}
