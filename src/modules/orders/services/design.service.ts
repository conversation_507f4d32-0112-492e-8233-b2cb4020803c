import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, ILike } from 'typeorm';
import { DesignEntity } from '../../../entities/design.entity';
import { DesignNameEntity } from '../../../entities/design-name.entity';

@Injectable()
export class DesignService {
  constructor(
    @InjectRepository(DesignEntity)
    private readonly designRepository: Repository<DesignEntity>,
    @InjectRepository(DesignNameEntity)
    private readonly designNameRepository: Repository<DesignNameEntity>,
  ) {}

  /**
   * Get Design from SKU
   *
   * @param sku - The SKU string to be processed
   * @returns The design name if found, otherwise null
   */
  async getDesignFromSku(sku: string): Promise<string | null> {
    if (!sku) return null;

    const parts = sku.split('-');

    for (const part of parts) {
      // Search for the design directly in the DesignEntity
      const designEntity = await this.designRepository
        .createQueryBuilder('designEntity')
        .where('LOWER(designEntity.design) = LOWER(:part)', { part })
        .getOne();

      if (designEntity) {
        return designEntity.design;
      }

      // Search for the design in the DesignNameEntity
      const designNameEntity = await this.designNameRepository.findOne({
        where: [{ value: ILike(part) }],
        relations: ['design'],
      });

      if (designNameEntity) {
        return designNameEntity.design?.design ?? null;
      }
    }

    return null;
  }

  /**
   * Create a new Design
   *
   * @param design - The design name to be created
   * @returns The created or existing design entity
   */
  async createDesign(design: string): Promise<DesignEntity> {
    let designEntity = await this.designRepository.findOne({
      where: { design },
    });

    if (!designEntity) {
      designEntity = this.designRepository.create({ design });
      await this.designRepository.save(designEntity);
    }

    return designEntity;
  }
}
