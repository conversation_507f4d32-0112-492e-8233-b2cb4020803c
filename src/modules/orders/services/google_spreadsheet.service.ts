import { Injectable, Logger, OnModuleInit } from '@nestjs/common';
import { GoogleSpreadsheet } from 'google-spreadsheet';
import { JWT } from 'google-auth-library';
import { DesignService } from './design.service';
import { Cron, CronExpression } from '@nestjs/schedule';
import { ProductService } from '../../product/services/product.service';

@Injectable()
export class GoogleSpreadsheetService implements OnModuleInit {
  private readonly logger = new Logger(GoogleSpreadsheetService.name);

  constructor(
    private readonly designService: DesignService,
    private readonly productService: ProductService,
  ) {}
  onModuleInit() {
    // this.updateSpreadsheetColumn();
  }

  /**
   * Cron Jobs - Scheduled Functions
   */

  // Load spreadsheet from Google every day at 11 PM
  @Cron(CronExpression.EVERY_DAY_AT_11PM)
  async loadSpreadSheetFromGoogle() {
    try {
      // Authenticate and initialize the Google Spreadsheet
      const serviceAccountAuth = new JWT({
        email: process.env.GOOGLE_SERVICE_ACCOUNT_EMAIL,
        key: process.env.GOOGLE_PRIVATE_KEY?.split(String.raw`\n`).join('\n'),
        scopes: ['https://www.googleapis.com/auth/spreadsheets'],
      });

      const doc = new GoogleSpreadsheet(
        process.env.GOOGLE_SPREADSHEET_ID ?? '',
        serviceAccountAuth,
      );

      await doc.loadInfo();

      const sheet = doc.sheetsByTitle['Designs'];
      if (!sheet) {
        this.logger.warn('Sheet "Designs" not found');
        return;
      }

      await sheet.loadCells(); // Load all cells in the sheet
      // Process each row in the spreadsheet
      for (let row = 1; row < sheet.rowCount; row++) {
        const cell = sheet.getCell(row, 0);
        if (
          cell.value !== null &&
          cell.value !== undefined &&
          !(cell.value as string).includes('-')
        ) {
          const designExists = await this.designService.getDesignFromSku(
            cell.value as string,
          );

          if (!designExists) {
            await this.designService.createDesign(cell.value as string);
            this.logger.warn(`Row ${row + 1}: ${cell.value}`);
          }
        }
      }
    } catch (error) {
      this.logger.error('Error loading spreadsheet from Google', error);
    }
  }

  // Load spreadsheet from Google every day at 11 PM
  async loadFromGoogle() {
    try {
      // Authenticate and initialize the Google Spreadsheet
      const serviceAccountAuth = new JWT({
        email: process.env.GOOGLE_SERVICE_ACCOUNT_EMAIL,
        key: process.env.GOOGLE_PRIVATE_KEY?.split(String.raw`\n`).join('\n'),
        scopes: ['https://www.googleapis.com/auth/spreadsheets'],
      });

      const doc = new GoogleSpreadsheet(
        process.env.GOOGLE_SPREADSHEET_ID ?? '',
        serviceAccountAuth,
      );

      await doc.loadInfo();

      const sheet = doc.sheetsByTitle['Designs'];
      if (!sheet) {
        this.logger.warn('Sheet "Designs" not found');
        return;
      }

      await sheet.loadCells(); // Load all cells in the sheet
      // Process each row in the spreadsheet
      for (let row = 1; row < sheet.rowCount; row++) {
        const cell = sheet.getCell(row, 0);
        if (
          cell.value !== null &&
          cell.value !== undefined &&
          !(cell.value as string).includes('-')
        ) {
          const designExists = await this.designService.getDesignFromSku(
            cell.value as string,
          );

          if (!designExists) {
            await this.designService.createDesign(cell.value as string);
            this.logger.warn(`Row ${row + 1}: ${cell.value}`);
          }
        }
      }
    } catch (error) {
      this.logger.error('Error loading spreadsheet from Google', error);
    }
  }

  /**
   * Update specific columns in a Google Spreadsheet based on the provided new value
   */
  async updateSpreadsheetColumn() {
    try {
      // Authenticate and initialize the Google Spreadsheet
      const serviceAccountAuth = new JWT({
        email: process.env.GOOGLE_SERVICE_ACCOUNT_EMAIL,
        key: process.env.GOOGLE_PRIVATE_KEY?.split(String.raw`\n`).join('\n'),
        scopes: ['https://www.googleapis.com/auth/spreadsheets'],
      });

      const doc = new GoogleSpreadsheet(
        process.env.INVENTORY_FILE_SPREADSHEET_ID ?? '',
        serviceAccountAuth,
      );

      await doc.loadInfo();

      this.logger.log(`Sheet count: ${doc.sheetCount}`);

      const sheet = doc.sheetsByTitle['<<FBM QTY UPDATE>>'];
      if (!sheet) {
        this.logger.warn(`Sheet "<<FBM QTY UPDATE>>" not found`);
        return;
      }

      const startRow = 3; // Row 4 in the sheet
      const batchSize = 500; // Number of rows to process at a time

      for (let start = startRow; start < sheet.rowCount; start += batchSize) {
        const end = Math.min(start + batchSize - 1, sheet.rowCount - 1);
        const range = `A${start + 1}:Q${end + 1}`;
        await sheet.loadCells(range);

        for (let row = start; row <= end; row++) {
          const sku = sheet.getCell(row, 0).value as string;

          if (sku?.length > 0) {
            await this.productService.findProduct({
              sku: sheet.getCell(row, 1).value as string,
              amazonUpdate: true,
            });
          }
        }
      }
    } catch (error) {
      this.logger.error('Error updating spreadsheet column', error);
    }
  }

  /**
   * Bulk update specific columns in a Google Spreadsheet
   */
  async bulkUpdateSpreadsheet(
    updates: { columnName: string; columnValue: string; newValue: string }[],
  ) {
    try {
      // Authenticate and initialize the Google Spreadsheet for the new API key
      const serviceAccountAuth = new JWT({
        email: process.env.GOOGLE_SERVICE_ACCOUNT_EMAIL,
        key: process.env.GOOGLE_PRIVATE_KEY?.split(String.raw`\n`).join('\n'),
        scopes: ['https://www.googleapis.com/auth/spreadsheets'],
      });

      const doc = new GoogleSpreadsheet(
        process.env.INVENTORY_FILE_SPREADSHEET_ID ?? '',
        serviceAccountAuth,
      );

      await doc.loadInfo();

      const sheet = doc.sheetsByTitle['YourSheetTitle'];
      if (!sheet) {
        this.logger.warn(`Sheet "YourSheetTitle" not found`);
        return;
      }

      await sheet.loadCells(); // Load all cells in the sheet

      for (const update of updates) {
        const { columnName, columnValue, newValue } = update;
        const columnIndex = sheet.headerValues.indexOf(columnName);
        if (columnIndex === -1) {
          this.logger.warn(`Column "${columnName}" not found`);
          continue;
        }

        // Process each row in the spreadsheet and update the value
        for (let row = 1; row < sheet.rowCount; row++) {
          const cell = sheet.getCell(row, columnIndex);
          if (cell.value === columnValue) {
            sheet.getCell(row, columnIndex).value = newValue;
            this.logger.warn(
              `Updated Row ${row + 1}: ${columnName} to ${newValue}`,
            );
          }
        }
      }

      await sheet.saveUpdatedCells(); // Save all changes
    } catch (error) {
      this.logger.error('Error performing bulk update on spreadsheet', error);
    }
  }
}
