import { Injectable, Logger } from '@nestjs/common';
import { Cron } from '@nestjs/schedule';
import { DataService } from './data.service';
import { MailerService } from '@nestjs-modules/mailer';
import {
  subDays,
  subWeeks,
  subMonths,
  endOfDay,
  startOfDay,
  endOfMonth,
  startOfMonth,
} from 'date-fns';

@Injectable()
export class MailService {
  private readonly logger = new Logger(MailService.name);

  constructor(
    private readonly dataService: DataService,
    private readonly mailerService: MailerService,
  ) {}

  @Cron('0 7 * * 1')
  async sendOverviewEmail(reportType: 'daily' | 'weekly' | 'monthly') {
    const {
      startDate,
      endDate,
      reportTitle,
      previousStartDate,
      previousEndDate,
    } = this.getReportDates(reportType);

    const currentPeriodData = await this.dataService.getTotalSalesAndQuantities(
      startDate,
      endDate,
    );
    const previousPeriodData =
      await this.dataService.getTotalSalesAndQuantities(
        previousStartDate,
        previousEndDate,
      );

    const currentSalesByPlatform = await this.dataService.getSalesByPlatform(
      startDate,
      endDate,
    );

    const previousSalesByPlatform = await this.dataService.getSalesByPlatform(
      previousStartDate,
      previousEndDate,
    );

    const salesByPlatform = this.dataService.compareSales(
      currentSalesByPlatform,
      previousSalesByPlatform,
    );

    const mostSalesByRingColorSize =
      await this.dataService.getMostSalesByRingColorSize(startDate, endDate);

    const previousMostSalesByRingColorSize =
      await this.dataService.getMostSalesByRingColorSize(
        previousStartDate,
        previousEndDate,
      );

    const mostDesignsFromOrders =
      await this.dataService.getMostDesignsFromOrders(startDate, endDate);

    const previousMostDesignsFromOrders =
      await this.dataService.getMostDesignsFromOrders(
        previousStartDate,
        previousEndDate,
      );

    const orderChange = this.dataService.calculatePercentageChange(
      currentPeriodData.totalOrders,
      previousPeriodData.totalOrders,
    );
    const quantityChange = this.dataService.calculatePercentageChange(
      currentPeriodData.totalQuantities,
      previousPeriodData.totalQuantities,
    );

    const topRingsWithChange = this.dataService.compareRings(
      mostSalesByRingColorSize,
      previousMostSalesByRingColorSize,
    );

    const topDesignsWithChange = this.dataService.compareDesigns(
      mostDesignsFromOrders,
      previousMostDesignsFromOrders,
    );

    const emailBody = this.generateEmailBody({
      reportTitle,
      currentPeriodData,
      orderChange,
      quantityChange,
      salesByPlatform,
      topRingsWithChange,
      startDate,
      endDate,
      topDesignsWithChange,
    });

    this.mailerService.sendMail({
      from: 'KnotTheory Dashboard',
      to: ['<EMAIL>', '<EMAIL>'],
      // to: ['<EMAIL>'],
      subject: `Overview for ${reportTitle}`,
      html: emailBody,
    });

    this.logger.debug(`Overview for ${reportTitle} sent.`);
  }

  getReportDates(reportType: 'daily' | 'weekly' | 'monthly') {
    const today = subDays(new Date(), 1);
    let startDate: Date;
    let endDate: Date;
    let previousStartDate: Date;
    let previousEndDate: Date;
    let reportTitle: string;

    switch (reportType) {
      case 'monthly':
        startDate = startOfMonth(today);
        endDate = endOfMonth(today);
        previousStartDate = subMonths(startDate, 1);
        previousEndDate = subMonths(endDate, 1);
        reportTitle = `Monthly Sales (from ${startDate
          .toISOString()
          .slice(0, 10)} to ${endDate.toISOString().slice(0, 10)})`;
        break;
      case 'daily':
        startDate = startOfDay(today);
        endDate = endOfDay(today);
        previousStartDate = subDays(startDate, 1);
        previousEndDate = subDays(endDate, 1);
        reportTitle = `Daily Sales - ${endDate.toISOString().slice(0, 10)}`;
        break;
      case 'weekly':
      default:
        startDate = subDays(today, 7);
        endDate = today;
        previousStartDate = subWeeks(startDate, 1);
        previousEndDate = subWeeks(endDate, 1);
        reportTitle = `Weekly Sales (from ${startDate
          .toISOString()
          .slice(0, 10)} to ${endDate.toISOString().slice(0, 10)})`;
        break;
    }

    return {
      startDate,
      endDate,
      reportTitle,
      previousStartDate,
      previousEndDate,
    };
  }

  generateEmailBody({
    reportTitle,
    currentPeriodData,
    orderChange,
    quantityChange,
    salesByPlatform,
    topRingsWithChange,
    startDate,
    endDate,
    topDesignsWithChange,
  }): string {
    const getColor = (isIncrease: boolean) => (isIncrease ? 'green' : 'red');
    const getIcon = (isIncrease: boolean) =>
      isIncrease ? '&#9650;' : '&#9660;';

    return `
    <h1>${reportTitle}</h1>
    <h2>Total Orders: ${
      currentPeriodData.totalOrders
    } <span style="color: ${getColor(orderChange.isIncrease)};">${getIcon(
      orderChange.isIncrease,
    )} ${orderChange.percentage.toFixed(2)}%</span></h2>
    <h2>Total Quantities: ${currentPeriodData.totalQuantities.toFixed(
      0,
    )} <span style="color: ${getColor(quantityChange.isIncrease)};">${getIcon(
      quantityChange.isIncrease,
    )} ${quantityChange.percentage.toFixed(2)}%</span></h2>
    <h3>Sales by Platform:</h3>
    <table style="width: 100%; border-collapse: collapse;">
        <thead style="background-color: lightgrey;">
            <tr>
                <th style="border: 1px solid #ddd; padding: 8px; text-align: left;">Platform</th>
                <th style="border: 1px solid #ddd; padding: 8px; text-align: left;">Sales</th>
                <th style="border: 1px solid #ddd; padding: 8px; text-align: left;">Previous Cycle</th>
                <th style="border: 1px solid #ddd; padding: 8px; text-align: left;">Change</th>
            </tr>
        </thead>
        <tbody>
        ${salesByPlatform
          .map(
            (platform) => `
          <tr>
              <td style="border: 1px solid #ddd; padding: 8px; color: ${this.getPlatformColor(
                platform.name,
              )};">&#9632; ${platform.name}</td>
              <td style="border: 1px solid #ddd; padding: 8px;">${platform.sales.toFixed(
                0,
              )}</td>
                <td style="border: 1px solid #ddd; padding: 8px; text-align: left;">${
                  platform.previousQty
                }</td>
              <td style="border: 1px solid #ddd; padding: 8px; text-align: left; color: ${getColor(
                platform.change.isIncrease,
              )};">${getIcon(
                platform.change.isIncrease,
              )} ${platform.change.percentage.toFixed(2)}%</td>
          </tr>`,
          )
          .join('')}
        </tbody>
    </table>
        <h3>Top 20 Designs by Sales (from ${startDate
          .toISOString()
          .slice(0, 10)} to ${endDate.toISOString().slice(0, 10)}):</h3>
    <table style="width: 100%; border-collapse: collapse;">
        <thead style="background-color: lightgrey;">
            <tr>
                <th style="border: 1px solid #ddd; padding: 8px; text-align: left;">Design</th>
                <th style="border: 1px solid #ddd; padding: 8px; text-align: left;">Total Sales</th>
                <th style="border: 1px solid #ddd; padding: 8px; text-align: left;">Previous Cycle</th>
                <th style="border: 1px solid #ddd; padding: 8px; text-align: left;">Change</th>
            </tr>
        </thead>
        <tbody>
        ${topDesignsWithChange
          .slice(0, 20)
          .map(
            (item) => `
          <tr>
              <td style="border: 1px solid #ddd; padding: 8px; text-align: left;">${
                item.design
              }</td>
              <td style="border: 1px solid #ddd; padding: 8px; text-align: left;">${
                item.qty
              }</td>
              <td style="border: 1px solid #ddd; padding: 8px; text-align: left;">${
                item.previousQty
              }</td>
              <td style="border: 1px solid #ddd; padding: 8px; text-align: left; color: ${getColor(
                item.change.isIncrease,
              )};">${getIcon(
                item.change.isIncrease,
              )} ${item.change.percentage.toFixed(2)}%</td>
          </tr>`,
          )
          .join('')}
        </tbody>
    </table>
    <h3>Top 10 Rings by Sales (from ${startDate
      .toISOString()
      .slice(0, 10)} to ${endDate.toISOString().slice(0, 10)}):</h3>
    <table style="width: 100%; border-collapse: collapse;">
        <thead style="background-color: lightgrey;">
            <tr>
                <th style="border: 1px solid #ddd; padding: 8px; text-align: left;">Style</th>
                <th style="border: 1px solid #ddd; padding: 8px; text-align: left;">Color</th>
                <th style="border: 1px solid #ddd; padding: 8px; text-align: left;">Size</th>
                <th style="border: 1px solid #ddd; padding: 8px; text-align: left;">Total Sales</th>
                <th style="border: 1px solid #ddd; padding: 8px; text-align: left;">Previous Cycle</th>
                <th style="border: 1px solid #ddd; padding: 8px; text-align: left;">Change</th>
            </tr>
        </thead>
        <tbody>
        ${topRingsWithChange
          .slice(0, 10)
          .map(
            (item) => `
          <tr>
              <td style="border: 1px solid #ddd; padding: 8px; text-align: left;">${
                item.style
              }</td>
              <td style="border: 1px solid #ddd; padding: 8px; text-align: left;">${
                item.color
              }</td>
              <td style="border: 1px solid #ddd; padding: 8px; text-align: left;">${
                item.size
              }</td>
              <td style="border: 1px solid #ddd; padding: 8px; text-align: left;">${
                item.qty
              }</td>
              <td style="border: 1px solid #ddd; padding: 8px; text-align: left;">${
                item.previousQty
              }</td>
              <td style="border: 1px solid #ddd; padding: 8px; text-align: left; color: ${getColor(
                item.change.isIncrease,
              )};">${getIcon(
                item.change.isIncrease,
              )} ${item.change.percentage.toFixed(2)}%</td>
          </tr>`,
          )
          .join('')}
        </tbody>
    </table>`;
  }

  private getPlatformColor(platformName: string): string {
    switch (true) {
      case platformName.toLowerCase().includes('shopify'):
        return 'green';
      case platformName.toLowerCase().includes('etsy'):
        return 'orange';
      case platformName.toLowerCase().includes('amazon'):
        return 'red';
      default:
        return 'black';
    }
  }
}
