import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';

import { DataController } from './controllers/data.controller';
import { OrderEntity } from '../../entities/order.entity';
import { ColorEntity } from '../../entities/color.entity';
import { ColorNameEntity } from '../../entities/color-name.entity';
import { SettingModule } from '../config/config.module';
import { DesignEntity } from '../../entities/design.entity';
import { DesignNameEntity } from '../../entities/design-name.entity';
import { ProductEntity } from '../../entities/product.entity';
import { ProductDesignEntity } from '../../entities/product-design.entity';
import { DataService } from './services/data.service';
import { ConfigService } from '../config/config.service';
import { ConfigEntity } from '../../entities/config.entity';
import { SoldProductEntity } from '../../entities/sold_product.entity';
import { MailerModule } from '@nestjs-modules/mailer';
import { GoogleSpreadsheetService } from './services/google_spreadsheet.service';
import { DesignService } from './services/design.service';
import { SkuModule } from '../sku/sku.module';
import { ProductService } from '../product/services/product.service';
import { MailService } from './services/mail.service';

@Module({
  imports: [
    TypeOrmModule.forFeature([
      ConfigEntity,
      OrderEntity,
      ProductEntity,
      ProductDesignEntity,
      ColorEntity,
      ColorNameEntity,
      DesignEntity,
      DesignNameEntity,
      SoldProductEntity,
    ]),
    MailerModule.forRoot({
      transport: {
        host: 'smtp.gmail.com',
        port: 465,
        secure: true,
        auth: {
          user: '<EMAIL>',
          pass: 'yqdcfawmcrfdctdh ',
        },
      },
    }),
    SettingModule,
    SkuModule,
  ],
  providers: [
    ProductService,
    DataService,
    MailService,
    ConfigService,
    DesignService,
    GoogleSpreadsheetService,
  ],
  exports: [DataService],
  controllers: [DataController],
})
export class DataModule {}
