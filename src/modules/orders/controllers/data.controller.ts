/* eslint-disable sonarjs/no-identical-functions */
import { Controller, Get, HttpException, Param, Query } from '@nestjs/common';
import { ApiTags } from '@nestjs/swagger';
import { DataService } from '../services/data.service';
import { type PageDto } from '../../../common/dto/page.dto';
import { RoleType } from '../../../common/constants';
import { MinifiedOrderDto } from '../dto/minified-order.dto';
import { OrderDto } from '../dto/order.dto';
import { PaginateOrdersOptionsDto } from '../dto/paginate-order-options.dto';
import { PaginateProductsOptionsDto } from '../dto/paginate-product-options.dto';
import { ApiPageOkResponse, Auth } from '../../../common/decorators';

@Controller('data')
@ApiTags('data')
export class DataController {
  constructor(private readonly dataService: DataService) {}

  @Get('orders')
  @Auth([RoleType.ADMIN])
  @ApiPageOkResponse({ type: OrderDto })
  async getOrders(
    @Query() pageOptions: PaginateOrdersOptionsDto,
    @Query('data') data?: string,
  ): Promise<PageDto<OrderDto>> {
    return this.dataService.getAllOrders(pageOptions, data);
  }

  @Get('ordersInfo')
  @Auth([RoleType.ADMIN])
  @ApiPageOkResponse({ type: OrderDto })
  async getOrdersInfo(@Query('data') data?: string) {
    return this.dataService.getAllOrderInfo(data);
  }

  @Get('topItems')
  @Auth([RoleType.ADMIN])
  @ApiPageOkResponse({ type: OrderDto })
  async topItems(@Query('data') data?: string) {
    return this.dataService.topItems(data);
  }

  @Get('getDesignInfo')
  @Auth([RoleType.ADMIN])
  @ApiPageOkResponse({ type: OrderDto })
  async getDesignInfo(@Query('data') data?: string) {
    return this.dataService.byDesign(data);
  }

  @Get('getMinifiedOrders')
  @Auth([RoleType.ADMIN])
  @ApiPageOkResponse({ type: MinifiedOrderDto })
  async getMinifiedOrders(
    @Query() pageOptions: PaginateOrdersOptionsDto,
  ): Promise<PageDto<MinifiedOrderDto>> {
    return this.dataService.getMinifiedOrders(pageOptions);
  }

  @Get('analysis')
  @Auth([RoleType.ADMIN])
  async getAnalysis(
    @Query() pageOptions: PaginateOrdersOptionsDto,
    @Query('data') data?: string,
  ) {
    return this.dataService.getMonthlySales(pageOptions, data);
  }

  @Get('topProducts')
  @Auth([RoleType.ADMIN])
  @ApiPageOkResponse({ type: MinifiedOrderDto })
  async topProducts(
    @Query() pageOptions: PaginateOrdersOptionsDto,
    @Query('basedOn') basedOn: string,
  ): Promise<PageDto<OrderDto>> {
    return this.dataService.topProducts(pageOptions, basedOn ?? ['style']);
  }

  @Get('byPlatform')
  @Auth([RoleType.ADMIN])
  async byPlatform(
    @Query('data') data: string,
    @Query('dateOption') dateOption?: string,
  ): Promise<any> {
    return this.dataService.byPlatform(data, dateOption);
  }

  @Get('byRegion')
  @Auth([RoleType.ADMIN])
  @ApiPageOkResponse({ type: MinifiedOrderDto })
  async byRegion(@Query('data') data?: string): Promise<any> {
    return this.dataService.byRegion(data);
  }

  @Get('orders/:id')
  @Auth([RoleType.ADMIN])
  @ApiPageOkResponse({ type: OrderDto })
  async getOrderDetails(@Param('id') orderId: string): Promise<OrderDto> {
    const order = await this.dataService.getOrderDetails(orderId);

    if (!order) {
      throw new HttpException('Order not found', 404);
    }

    return order;
  }

  @Get('orders/:id/items')
  @Auth([RoleType.ADMIN])
  @ApiPageOkResponse({ type: OrderDto })
  async getOrderItems(@Param('id') orderId: string): Promise<OrderDto[]> {
    const items = await this.dataService.getOrderItems(`#${orderId}`);

    if (!items || items.length === 0) {
      return [];
    }

    return items;
  }

  @Get('salesInfo')
  @Auth([RoleType.ADMIN])
  async salesInfo(
    @Query() pageOptions: PaginateProductsOptionsDto,
    @Query('data') data?: string,
  ) {
    return this.dataService.getSalesInfo(pageOptions, data);
  }

  @Get('getSalesVelocity')
  @Auth([RoleType.ADMIN])
  async getSalesVelocity(@Query('data') data?: string) {
    return this.dataService.getSalesVelocity(data);
  }

  @Get('order-statistics')
  @Auth([RoleType.ADMIN])
  async getOrderStatistics(
    @Query() paginateOptions: PaginateProductsOptionsDto,
    @Query('sortField') sortField: string = 'order_count',
    @Query('sortOrder') sortOrder: 'ASC' | 'DESC' = 'DESC',
    @Query('data') data?: string,
  ): Promise<PageDto<any>> {
    return this.dataService.getOrderStatistics(
      paginateOptions,
      sortField,
      sortOrder,
      data,
    );
  }

  @Get('unsaved-items')
  @Auth([RoleType.ADMIN, RoleType.USER])
  async getUnsavedProducts(
    @Query() paginateOptions: PaginateProductsOptionsDto,
    @Query('data') data?: string,
  ): Promise<PageDto<any>> {
    return this.dataService.unsavedItems(paginateOptions, data);
  }

  @Get('engravedSalesInfo')
  @Auth([RoleType.ADMIN])
  async engravedSalesInfo(
    @Query() pageOptions: PaginateProductsOptionsDto,
    @Query('data') data?: string,
  ) {
    return this.dataService.getEngravedSalesInfo(pageOptions, data);
  }
}
