import { Controller, Get, HttpCode, HttpStatus } from '@nestjs/common';

import { RoleType } from '../../../common/constants';
import { UserEntity } from '../../../entities/user.entity';
import { Auth, AuthUser } from '../../../common/decorators';

@Controller('users')
export class UserController {
  @Get('admin')
  @Auth([RoleType.USER])
  @HttpCode(HttpStatus.OK)
  async admin(@AuthUser() user: UserEntity) {
    return {
      text: `Admin ${user.firstName}`,
    };
  }
}
