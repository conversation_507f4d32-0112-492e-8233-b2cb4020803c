import { RoleType } from '../../../common/constants';
import { AbstractDto } from '../../../common/dto/abstract.dto';
import { type UserEntity } from '../../../entities/user.entity';

// TODO, remove this class and use constructor's second argument's type
export type UserDtoOptions = Partial<{ isActive: boolean }>;

export class UserDto extends AbstractDto {
  firstName?: string | null;

  lastName?: string | null;

  username!: string;

  role!: RoleType;

  email?: string | null;

  avatar?: string | null;

  isActive?: boolean;

  constructor(user: UserEntity, options?: UserDtoOptions) {
    super(user);
    this.firstName = user.firstName;
    this.lastName = user.lastName;
    this.role = user.role;
    this.email = user.email;
    this.avatar = user.avatar;
    this.isActive = options?.isActive;
  }
}
