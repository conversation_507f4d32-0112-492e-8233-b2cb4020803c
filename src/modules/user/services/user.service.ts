import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { type FindOptionsWhere, Repository } from 'typeorm';
import { Transactional } from 'typeorm-transactional';

import { UserRegisterDto } from '../../auth/dto/user-register.dto';
import { type UserDto } from '../dtos/user.dto';
import { UserEntity } from '../../../entities/user.entity';
import { UserNotFoundException } from '../../../common/exceptions/user-not-found.exception';
import * as bcrypt from 'bcrypt';
import { ResetPasswordDto } from '../../auth/dto/reset-pass.dto';

@Injectable()
export class UserService {
  constructor(
    @InjectRepository(UserEntity)
    private userRepository: Repository<UserEntity>,
  ) {}

  /**
   * Find single user
   */
  findOne(findData: FindOptionsWhere<UserEntity>): Promise<UserEntity | null> {
    return this.userRepository.findOneBy(findData);
  }

  @Transactional()
  async createUser(userRegisterDto: UserRegisterDto): Promise<UserEntity> {
    const user = this.userRepository.create(userRegisterDto);
    user.password = await bcrypt.hash(user.password, 10);
    await this.userRepository.save(user);
    return user;
  }

  async getUser(userId: Uuid): Promise<UserDto> {
    const queryBuilder = this.userRepository.createQueryBuilder('user');

    queryBuilder.where('user.id = :userId', { userId });

    const userEntity = await queryBuilder.getOne();

    if (!userEntity) {
      throw new UserNotFoundException();
    }

    return userEntity.toDto();
  }

  async resetPassword(resetPasswordDto: ResetPasswordDto): Promise<UserDto> {
    const user = await this.userRepository.findOneBy({
      email: resetPasswordDto.email,
    });
    if (!user) {
      throw new UserNotFoundException();
    }
    user.password = await bcrypt.hash(resetPasswordDto.password, 10);
    await this.userRepository.save(user);
    return user.toDto();
  }
}
