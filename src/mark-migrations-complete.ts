import { DataSource } from 'typeorm';
import { config } from 'dotenv';
import { SnakeNamingStrategy } from './config/snake-naming.strategy';

// Load environment variables
config();

// Create a data source
const dataSource = new DataSource({
  type: 'postgres',
  host: process.env.DB_HOST,
  port: Number(process.env.DB_PORT),
  username: process.env.DB_USERNAME,
  password: process.env.DB_PASSWORD,
  database: process.env.DB_DATABASE,
  namingStrategy: new SnakeNamingStrategy(),
});

async function markMigrationsAsComplete() {
  try {
    // Initialize the data source
    const initializedDataSource = await dataSource.initialize();
    console.log('Database connection initialized');

    // Get current migration status
    const migrations = await initializedDataSource.query(
      'SELECT * FROM migrations',
    );
    console.log(
      `Found ${migrations.length} existing migrations in the database`,
    );

    // Oldest migrations that need to be marked as completed
    const oldestMigrations = [
      { timestamp: '1622299665807', name: 'createUsersTable1622299665807' },
      { timestamp: '1639940635548', name: 'addUserSettings1639940635548' },
      { timestamp: '1641994291086', name: 'addPostsTable1641994291086' },
    ];

    // Insert each of the oldest migrations if they don't exist
    for (const migration of oldestMigrations) {
      const exists = migrations.some((m) => m.name === migration.name);

      if (!exists) {
        await initializedDataSource.query(
          'INSERT INTO migrations(timestamp, name) VALUES ($1, $2)',
          [migration.timestamp, migration.name],
        );
        console.log(`Marked oldest migration as complete: ${migration.name}`);
      } else {
        console.log(`Migration already exists: ${migration.name}`);
      }
    }

    // Also make sure the newest migration is marked
    const newestMigration = {
      timestamp: '1743807319814',
      name: 'removeUnusedIndexes1743807319814',
    };

    const newestExists = migrations.some(
      (m) => m.name === newestMigration.name,
    );
    if (!newestExists) {
      await initializedDataSource.query(
        'INSERT INTO migrations(timestamp, name) VALUES ($1, $2)',
        [newestMigration.timestamp, newestMigration.name],
      );
      console.log(
        `Marked newest migration as complete: ${newestMigration.name}`,
      );
    } else {
      console.log(`Newest migration already exists: ${newestMigration.name}`);
    }

    // Verify migrations table
    const updatedMigrations = await initializedDataSource.query(
      'SELECT * FROM migrations',
    );
    console.log(`Now have ${updatedMigrations.length} migrations in the table`);

    await initializedDataSource.destroy();
    console.log('Database connection closed');
    process.exit(0);
  } catch (error) {
    console.error('Error marking migrations as complete', error);
    process.exit(1);
  }
}

// Run the function
markMigrationsAsComplete().catch(console.error);
