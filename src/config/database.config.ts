import { TypeOrmModuleOptions } from '@nestjs/typeorm';

/**
 * Database configuration options
 * These settings are merged with the dynamic configuration from ApiConfigService
 */
export const databaseConfig: Partial<TypeOrmModuleOptions> = {
  // Performance optimizations
  autoLoadEntities: true,
  entitySkipConstructor: true,

  // Connection pool settings
  extra: {
    max: 20, // Maximum number of connections in the pool
    min: 5, // Minimum number of connections in the pool
    idleTimeoutMillis: 30000, // How long a connection can be idle before being removed
    connectionTimeoutMillis: 2000, // How long to wait for a connection
  },

  // Logging and debugging
  verboseRetryLog: true,
  maxQueryExecutionTime: 60000,

  // Connection retry settings
  retryAttempts: 5,
  retryDelay: 5000,

  // Additional safety settings
  synchronize: false,
  migrationsRun: true,
};
