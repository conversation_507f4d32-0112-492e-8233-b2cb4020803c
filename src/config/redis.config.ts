import { CacheModuleOptions } from '@nestjs/cache-manager';

/**
 * Redis cache configuration options
 * Environment variables are loaded from .env file
 */
export const redisConfig: CacheModuleOptions = {
  host: process.env.REDIS_HOST,
  port: Number(process.env.REDIS_PORT),
  username: process.env.REDIS_USERNAME,
  password: process.env.REDIS_PASSWORD,
  no_ready_check: true,

  // Additional Redis configuration
  ttl: 60 * 60 * 24, // 24 hours default TTL
  max: 100, // maximum number of items in cache
  isGlobal: true,

  // Memory management settings
  maxmemory: '256mb', // Maximum memory usage
  maxmemoryPolicy: 'allkeys-lru', // Remove least recently used keys when memory limit is reached
  maxmemorySamples: 10, // Number of samples to check for LRU eviction
};
