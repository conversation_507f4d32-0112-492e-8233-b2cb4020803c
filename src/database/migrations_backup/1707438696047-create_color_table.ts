import { MigrationInterface, QueryRunner } from "typeorm";

export class CreateColorTable1707438696047 implements MigrationInterface {
    name = 'CreateColorTable1707438696047'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`CREATE TABLE "colors" ("id" SERIAL NOT NULL, "color" character varying NOT NULL, CONSTRAINT "PK_3a62edc12d29307872ab1777ced" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "color_names" ("id" SERIAL NOT NULL, "value" character varying NOT NULL, "color_id" integer, CONSTRAINT "PK_602575f1a20243dcab16ca91a87" PRIMARY KEY ("id"))`);
        await queryRunner.query(`ALTER TABLE "color_names" ADD CONSTRAINT "FK_c2084b667e2afa1ecbedd7de9f2" FOREIGN KEY ("color_id") REFERENCES "colors"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "color_names" DROP CONSTRAINT "FK_c2084b667e2afa1ecbedd7de9f2"`);
        await queryRunner.query(`DROP TABLE "color_names"`);
        await queryRunner.query(`DROP TABLE "colors"`);
    }

}
