import { MigrationInterface, QueryRunner } from "typeorm";

export class ModifyProducts1711585234467 implements MigrationInterface {
    name = 'ModifyProducts1711585234467'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "product-design" DROP CONSTRAINT "FK_61d81805220b9b7a9ef8d02925c"`);
        await queryRunner.query(`ALTER TABLE "product-design" DROP COLUMN "product_sku"`);
        await queryRunner.query(`ALTER TABLE "products" ALTER COLUMN "sku" DROP NOT NULL`);
        await queryRunner.query(`ALTER TABLE "products" DROP CONSTRAINT "PK_2062733f48d2131f2076a87876b"`);
        await queryRunner.query(`ALTER TABLE "products" ADD CONSTRAINT "PK_0806c755e0aca124e67c0cf6d7d" PRIMARY KEY ("id")`);
        await queryRunner.query(`ALTER TABLE "product-design" ADD CONSTRAINT "FK_aacaa61a44e4d82de300a55a112" FOREIGN KEY ("product_id") REFERENCES "products"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "product-design" DROP CONSTRAINT "FK_aacaa61a44e4d82de300a55a112"`);
        await queryRunner.query(`ALTER TABLE "products" DROP CONSTRAINT "PK_0806c755e0aca124e67c0cf6d7d"`);
        await queryRunner.query(`ALTER TABLE "products" ADD CONSTRAINT "PK_2062733f48d2131f2076a87876b" PRIMARY KEY ("id", "sku")`);
        await queryRunner.query(`ALTER TABLE "products" ALTER COLUMN "sku" SET NOT NULL`);
        await queryRunner.query(`ALTER TABLE "product-design" ADD "product_sku" character varying`);
        await queryRunner.query(`ALTER TABLE "product-design" ADD CONSTRAINT "FK_61d81805220b9b7a9ef8d02925c" FOREIGN KEY ("product_id", "product_sku") REFERENCES "products"("id","sku") ON DELETE NO ACTION ON UPDATE NO ACTION`);
    }

}
