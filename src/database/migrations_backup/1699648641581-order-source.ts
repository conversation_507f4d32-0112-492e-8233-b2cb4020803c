import { type MigrationInterface, type QueryRunner } from 'typeorm';

export class OrderSource1699648641581 implements MigrationInterface {
  name = 'OrderSource1699648641581';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "orders" ADD "source" character varying NOT NULL DEFAULT 'shopify'`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "orders" DROP COLUMN "source"`);
  }
}
