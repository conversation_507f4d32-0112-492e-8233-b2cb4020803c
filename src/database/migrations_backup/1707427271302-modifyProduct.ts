import { MigrationInterface, QueryRunner } from "typeorm";

export class ModifyProduct1707427271302 implements MigrationInterface {
    name = 'ModifyProduct1707427271302'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "products" ADD "amazon_quantity" integer`);
        await queryRunner.query(`ALTER TABLE "products" ADD "etsy_quantity" integer`);
        await queryRunner.query(`ALTER TABLE "products" ADD "shopify_quantity" integer`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "products" DROP COLUMN "shopify_quantity"`);
        await queryRunner.query(`ALTER TABLE "products" DROP COLUMN "etsy_quantity"`);
        await queryRunner.query(`ALTER TABLE "products" DROP COLUMN "amazon_quantity"`);
    }

}
