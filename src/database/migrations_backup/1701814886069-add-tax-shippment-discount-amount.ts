import { type MigrationInterface, type QueryRunner } from 'typeorm';

export class AddTaxShippmentDiscountAmount1701814886069
  implements MigrationInterface
{
  name = 'AddTaxShippmentDiscountAmount1701814886069';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "orders" ADD "total_discounts" numeric`,
    );
    await queryRunner.query(
      `ALTER TABLE "orders" ADD "total_shipping_price_set" numeric`,
    );
    await queryRunner.query(`ALTER TABLE "orders" ADD "total_tax" numeric`);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "orders" DROP COLUMN "total_tax"`);
    await queryRunner.query(
      `ALTER TABLE "orders" DROP COLUMN "total_shipping_price_set"`,
    );
    await queryRunner.query(
      `ALTER TABLE "orders" DROP COLUMN "total_discounts"`,
    );
  }
}
