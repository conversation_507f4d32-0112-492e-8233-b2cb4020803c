import { MigrationInterface, QueryRunner } from "typeorm";

export class ModifyProducts1711584395985 implements MigrationInterface {
    name = 'ModifyProducts1711584395985'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "products" DROP COLUMN "external_product_id"`);
        await queryRunner.query(`ALTER TABLE "products" ADD "external_product_id" character varying NOT NULL DEFAULT '-'`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "products" DROP COLUMN "external_product_id"`);
        await queryRunner.query(`ALTER TABLE "products" ADD "external_product_id" text NOT NULL`);
    }

}
