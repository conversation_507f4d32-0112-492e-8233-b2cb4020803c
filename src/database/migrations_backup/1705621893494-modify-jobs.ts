import { MigrationInterface, QueryRunner } from "typeorm";

export class ModifyJobs1705621893494 implements MigrationInterface {
    name = 'ModifyJobs1705621893494'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "jobs" ALTER COLUMN "error" DROP NOT NULL`);
        await queryRunner.query(`ALTER TABLE "jobs" ALTER COLUMN "params" DROP NOT NULL`);
        await queryRunner.query(`ALTER TABLE "jobs" ALTER COLUMN "headers" DROP NOT NULL`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "jobs" ALTER COLUMN "headers" SET NOT NULL`);
        await queryRunner.query(`ALTER TABLE "jobs" ALTER COLUMN "params" SET NOT NULL`);
        await queryRunner.query(`ALTER TABLE "jobs" ALTER COLUMN "error" SET NOT NULL`);
    }

}
