import { MigrationInterface, QueryRunner } from "typeorm";

export class ModifyOrders1705979156005 implements MigrationInterface {
    name = 'ModifyOrders1705979156005'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "orders" ADD "saved" boolean NOT NULL DEFAULT false`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "orders" DROP COLUMN "saved"`);
    }

}
