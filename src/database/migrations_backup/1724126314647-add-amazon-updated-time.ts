import { MigrationInterface, QueryRunner } from "typeorm";

export class AddAmazonUpdatedTime1724126314647 implements MigrationInterface {
    name = 'AddAmazonUpdatedTime1724126314647'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "products" ADD "amazon_updated_time" TIMESTAMP`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "products" DROP COLUMN "amazon_updated_time"`);
    }

}
