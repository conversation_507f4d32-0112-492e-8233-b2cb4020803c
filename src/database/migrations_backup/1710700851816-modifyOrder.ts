import { MigrationInterface, QueryRunner } from "typeorm";

export class ModifyOrder1710700851816 implements MigrationInterface {
    name = 'ModifyOrder1710700851816'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "orders" RENAME COLUMN "data_json" TO "data"`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "orders" RENAME COLUMN "data" TO "data_json"`);
    }

}
