import { type MigrationInterface, type QueryRunner } from 'typeorm';

export class InternalOrderStatus1699642698414 implements MigrationInterface {
  name = 'InternalOrderStatus1699642698414';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "orders" ADD "status" character varying NOT NULL DEFAULT 'initiated'`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "orders" DROP COLUMN "status"`);
  }
}
