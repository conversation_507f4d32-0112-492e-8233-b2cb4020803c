import { MigrationInterface, QueryRunner } from "typeorm";

export class ModifyOrderTable1701815739640 implements MigrationInterface {
    name = 'ModifyOrderTable1701815739640'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "orders" RENAME COLUMN "total_shipping_price_set" TO "total_shipping_price"`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "orders" RENAME COLUMN "total_shipping_price" TO "total_shipping_price_set"`);
    }

}
