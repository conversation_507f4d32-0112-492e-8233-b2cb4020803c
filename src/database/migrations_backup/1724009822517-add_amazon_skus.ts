import { MigrationInterface, QueryRunner } from "typeorm";

export class AddAmazonSkus1724009822517 implements MigrationInterface {
    name = 'AddAmazonSkus1724009822517'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "products" ADD "amazon_skus" character varying`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "products" DROP COLUMN "amazon_skus"`);
    }

}
