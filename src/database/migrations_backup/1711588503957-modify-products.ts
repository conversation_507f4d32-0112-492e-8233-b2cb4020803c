import { MigrationInterface, QueryRunner } from "typeorm";

export class ModifyProducts1711588503957 implements MigrationInterface {
    name = 'ModifyProducts1711588503957'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`CREATE TABLE "product_design" ("id" SERIAL NOT NULL, "title" character varying NOT NULL DEFAULT '', "quantity" integer NOT NULL DEFAULT '0', "product_id" uuid, "product_sku" character varying, CONSTRAINT "PK_3d12e5ac2924935dd903ef576b0" PRIMARY KEY ("id"))`);
        await queryRunner.query(`ALTER TABLE "product_design" ADD CONSTRAINT "FK_48546a50c7a92dae02a50ad4784" FOREIGN KEY ("product_id", "product_sku") REFERENCES "products"("id","sku") ON DELETE NO ACTION ON UPDATE NO ACTION`);
    }

    public async down(queryRunner: Query<PERSON>unner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "product_design" DROP CONSTRAINT "FK_48546a50c7a92dae02a50ad4784"`);
        await queryRunner.query(`DROP TABLE "product_design"`);
    }

}
