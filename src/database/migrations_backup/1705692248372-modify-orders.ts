import { MigrationInterface, QueryRunner } from "typeorm";

export class ModifyOrders1705692248372 implements MigrationInterface {
    name = 'ModifyOrders1705692248372'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "orders" ADD "status" character varying`);
        await queryRunner.query(`ALTER TABLE "orders" ADD "city" character varying`);
        await queryRunner.query(`ALTER TABLE "orders" ADD "province" character varying`);
        await queryRunner.query(`ALTER TABLE "orders" ADD "country" character varying`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "orders" DROP COLUMN "country"`);
        await queryRunner.query(`ALTER TABLE "orders" DROP COLUMN "province"`);
        await queryRunner.query(`ALTER TABLE "orders" DROP COLUMN "city"`);
        await queryRunner.query(`ALTER TABLE "orders" DROP COLUMN "status"`);
    }

}
