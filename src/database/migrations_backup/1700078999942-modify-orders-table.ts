import { MigrationInterface, QueryRunner } from 'typeorm';

export class ModifyOrdersTable21700078999942 implements MigrationInterface {
  name = 'ModifyOrdersTable21700078999942';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "orders" ADD "external_order_id" text NOT NULL`,
    );
    await queryRunner.query(
      `ALTER TABLE "orders" ADD "external_created_at" TIMESTAMP NOT NULL`,
    );
    await queryRunner.query(
      `ALTER TABLE "orders" ADD "external_price" numeric NOT NULL`,
    );
    await queryRunner.query(
      `ALTER TABLE "orders" ADD "external_currency" character varying NOT NULL`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "orders" DROP COLUMN "external_currency"`,
    );
    await queryRunner.query(
      `ALTER TABLE "orders" DROP COLUMN "external_price"`,
    );
    await queryRunner.query(
      `ALTER TABLE "orders" DROP COLUMN "external_created_at"`,
    );
    await queryRunner.query(
      `ALTER TABLE "orders" DROP COLUMN "external_order_id"`,
    );
  }
}
