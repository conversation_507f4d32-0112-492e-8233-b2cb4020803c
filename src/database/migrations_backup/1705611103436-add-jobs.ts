import { MigrationInterface, QueryRunner } from "typeorm";

export class AddJobs1705611103436 implements MigrationInterface {
    name = 'AddJobs1705611103436'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`CREATE TABLE "jobs" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "created_at" TIMESTAMP NOT NULL DEFAULT now(), "updated_at" TIMESTAMP NOT NULL DEFAULT now(), "source" text NOT NULL, "type" text NOT NULL, "params" text NOT NULL, "headers" text NOT NULL, "url" text NOT NULL, CONSTRAINT "PK_cf0a6c42b72fcc7f7c237def345" PRIMARY KEY ("id"))`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`DROP TABLE "jobs"`);
    }

}
