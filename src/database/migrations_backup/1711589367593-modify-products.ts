import { MigrationInterface, QueryRunner } from 'typeorm';

export class ModifyProducts1711589367593 implements MigrationInterface {
  name = 'ModifyProducts1711589367593';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `CREATE TABLE "product_designs" ("id" SERIAL NOT NULL, "title" character varying NOT NULL DEFAULT '', "quantity" integer NOT NULL DEFAULT '0', "product_id" uuid, "product_sku" character varying, CONSTRAINT "PK_14326114561b24d7e84537b6d69" PRIMARY KEY ("id"))`,
    );
    await queryRunner.query(
      `ALTER TABLE "product_designs" ADD CONSTRAINT "FK_d80829ca3f0e69c33066154bd2c" FOREIGN KEY ("product_id", "product_sku") REFERENCES "products"("id","sku") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "product_designs" DROP CONSTRAINT "FK_d80829ca3f0e69c33066154bd2c"`,
    );
    await queryRunner.query(`DROP TABLE "product_designs"`);
  }
}
