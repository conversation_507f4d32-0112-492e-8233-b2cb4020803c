import { MigrationInterface, QueryRunner } from "typeorm";

export class ModifyProductTable1700514544707 implements MigrationInterface {
    name = 'ModifyProductTable1700514544707'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "products" ADD "sku" character varying NOT NULL`);
        await queryRunner.query(`ALTER TABLE "products" ADD "size" integer NOT NULL`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "products" DROP COLUMN "size"`);
        await queryRunner.query(`ALTER TABLE "products" DROP COLUMN "sku"`);
    }

}
