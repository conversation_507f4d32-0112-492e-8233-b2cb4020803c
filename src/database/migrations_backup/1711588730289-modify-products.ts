import { MigrationInterface, QueryRunner } from "typeorm";

export class ModifyProducts1711588730289 implements MigrationInterface {
    name = 'ModifyProducts1711588730289'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "product_design" DROP CONSTRAINT "FK_48546a50c7a92dae02a50ad4784"`);
        await queryRunner.query(`ALTER TABLE "product_design" DROP COLUMN "product_sku"`);
        await queryRunner.query(`ALTER TABLE "products" DROP CONSTRAINT "PK_2062733f48d2131f2076a87876b"`);
        await queryRunner.query(`ALTER TABLE "products" ADD CONSTRAINT "PK_0806c755e0aca124e67c0cf6d7d" PRIMARY KEY ("id")`);
        await queryRunner.query(`ALTER TABLE "product_design" ADD CONSTRAINT "FK_0a695b49de2df8ce5d5b1272078" FOREIGN KEY ("product_id") REFERENCES "products"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "product_design" DROP CONSTRAINT "FK_0a695b49de2df8ce5d5b1272078"`);
        await queryRunner.query(`ALTER TABLE "products" DROP CONSTRAINT "PK_0806c755e0aca124e67c0cf6d7d"`);
        await queryRunner.query(`ALTER TABLE "products" ADD CONSTRAINT "PK_2062733f48d2131f2076a87876b" PRIMARY KEY ("id", "sku")`);
        await queryRunner.query(`ALTER TABLE "product_design" ADD "product_sku" character varying`);
        await queryRunner.query(`ALTER TABLE "product_design" ADD CONSTRAINT "FK_48546a50c7a92dae02a50ad4784" FOREIGN KEY ("product_id", "product_sku") REFERENCES "products"("id","sku") ON DELETE NO ACTION ON UPDATE NO ACTION`);
    }

}
