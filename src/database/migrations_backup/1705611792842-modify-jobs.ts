import { MigrationInterface, QueryRunner } from "typeorm";

export class ModifyJobs1705611792842 implements MigrationInterface {
    name = 'ModifyJobs1705611792842'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "jobs" ADD "error" text NOT NULL`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "jobs" DROP COLUMN "error"`);
    }

}
