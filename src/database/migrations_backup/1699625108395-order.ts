/* eslint-disable max-len */
import { type MigrationInterface, type QueryRunner } from 'typeorm';

export class Order1699625108395 implements MigrationInterface {
  name = 'Order1699625108395';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `CREATE TABLE "orders" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "created_at" TIMESTAMP NOT NULL DEFAULT now(), "updated_at" TIMESTAMP NOT NULL DEFAULT now(), "external_order_id" text NOT NULL, "external_created_at" TIMESTAMP NOT NULL, "external_price" numeric NOT NULL, "external_currency" character varying NOT NULL, "data_json" text NOT NULL, "hash" character varying NOT NULL, CONSTRAINT "PK_710e2d4957aa5878dfe94e4ac2f" PRIMARY KEY ("id"))`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`DROP TABLE "orders"`);
  }
}
