import { MigrationInterface, QueryRunner } from "typeorm";

export class Modifyorders1706652199823 implements MigrationInterface {
    name = 'Modifyorders1706652199823'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "orders" ADD "fulfillment_channel" character varying`);
        await queryRunner.query(`ALTER TABLE "orders" ADD "sales_channel" character varying`);
        await queryRunner.query(`ALTER TABLE "orders" ALTER COLUMN "email" DROP NOT NULL`);
        await queryRunner.query(`ALTER TABLE "orders" ALTER COLUMN "note" DROP NOT NULL`);
        await queryRunner.query(`ALTER TABLE "orders" ALTER COLUMN "message" DROP NOT NULL`);
        await queryRunner.query(`ALTER TABLE "orders" ALTER COLUMN "outside" DROP NOT NULL`);
        await queryRunner.query(`ALTER TABLE "orders" ALTER COLUMN "inside" DROP NOT NULL`);
        await queryRunner.query(`ALTER TABLE "orders" ALTER COLUMN "gold_inlay" DROP NOT NULL`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "orders" ALTER COLUMN "gold_inlay" SET NOT NULL`);
        await queryRunner.query(`ALTER TABLE "orders" ALTER COLUMN "inside" SET NOT NULL`);
        await queryRunner.query(`ALTER TABLE "orders" ALTER COLUMN "outside" SET NOT NULL`);
        await queryRunner.query(`ALTER TABLE "orders" ALTER COLUMN "message" SET NOT NULL`);
        await queryRunner.query(`ALTER TABLE "orders" ALTER COLUMN "note" SET NOT NULL`);
        await queryRunner.query(`ALTER TABLE "orders" ALTER COLUMN "email" SET NOT NULL`);
        await queryRunner.query(`ALTER TABLE "orders" DROP COLUMN "sales_channel"`);
        await queryRunner.query(`ALTER TABLE "orders" DROP COLUMN "fulfillment_channel"`);
    }

}
