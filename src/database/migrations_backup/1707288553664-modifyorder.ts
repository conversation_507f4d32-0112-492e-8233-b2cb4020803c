import { MigrationInterface, QueryRunner } from "typeorm";

export class Modifyorder1707288553664 implements MigrationInterface {
    name = 'Modifyorder1707288553664'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "orders" ADD "is_mixed_order" boolean NOT NULL DEFAULT false`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "orders" DROP COLUMN "is_mixed_order"`);
    }

}
