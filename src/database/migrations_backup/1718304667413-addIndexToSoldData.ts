import { MigrationInterface, QueryRunner } from "typeorm";

export class AddIndexToSoldData1718304667413 implements MigrationInterface {
    name = 'AddIndexToSoldData1718304667413'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`CREATE INDEX "IDX_be2d8b15a3ae31273a8f1062f5" ON "sold_products" ("style") `);
        await queryRunner.query(`CREATE INDEX "IDX_ca8c550c9a7e41f2a46cca2ccc" ON "sold_products" ("color") `);
        await queryRunner.query(`CREATE INDEX "IDX_d71582f28a43970104cc8bc76e" ON "sold_products" ("inventory_item_id") `);
        await queryRunner.query(`CREATE INDEX "IDX_72830378fa69d60f1e2adf0572" ON "sold_products" ("external_product_id") `);
        await queryRunner.query(`CREATE INDEX "IDX_7ae58b3a0ca453178eadf2ffd3" ON "sold_products" ("sales_date") `);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`DROP INDEX "public"."IDX_7ae58b3a0ca453178eadf2ffd3"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_72830378fa69d60f1e2adf0572"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_d71582f28a43970104cc8bc76e"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_ca8c550c9a7e41f2a46cca2ccc"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_be2d8b15a3ae31273a8f1062f5"`);
    }

}
