import { MigrationInterface, QueryRunner } from "typeorm";

export class ModifyProduct1711226425710 implements MigrationInterface {
    name = 'ModifyProduct1711226425710'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "products" ADD "inventory_management" character varying`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "products" DROP COLUMN "inventory_management"`);
    }

}
