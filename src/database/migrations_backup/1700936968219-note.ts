import { type MigrationInterface, type QueryRunner } from 'typeorm';

export class Note1700936968219 implements MigrationInterface {
  name = 'Note1700936968219';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "products" ADD "note" text`);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "products" DROP COLUMN "note"`);
  }
}
