import { MigrationInterface, QueryRunner } from "typeorm";

export class AddConfigEntity1717004686734 implements MigrationInterface {
    name = 'AddConfigEntity1717004686734'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`CREATE TABLE "configurations" ("id" SERIAL NOT NULL, "key" character varying NOT NULL, "value" text NOT NULL, "description" text, CONSTRAINT "UQ_3c658898252e3694655de8a07e7" UNIQUE ("key"), CONSTRAINT "PK_ef9fc29709cc5fc66610fc6a664" PRIMARY KEY ("id"))`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`DROP TABLE "configurations"`);
    }

}
