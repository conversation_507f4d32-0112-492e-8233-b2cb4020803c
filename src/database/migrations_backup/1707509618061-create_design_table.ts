import { MigrationInterface, QueryRunner } from "typeorm";

export class CreateDesignTable1707509618061 implements MigrationInterface {
    name = 'CreateDesignTable1707509618061'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`CREATE TABLE "designs" ("id" SERIAL NOT NULL, "design" character varying NOT NULL, "code" character varying, CONSTRAINT "PK_3679aaa73bc37ec35a24a3cfde8" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "design_names" ("id" SERIAL NOT NULL, "value" character varying NOT NULL, "design_id" integer, CONSTRAINT "PK_0f27e0f294e2e73d7241435aefa" PRIMARY KEY ("id"))`);
        await queryRunner.query(`ALTER TABLE "design_names" ADD CONSTRAINT "FK_a2333ca03839121ac97728e48ed" FOREIGN KEY ("design_id") REFERENCES "designs"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "design_names" DROP CONSTRAINT "FK_a2333ca03839121ac97728e48ed"`);
        await queryRunner.query(`DROP TABLE "design_names"`);
        await queryRunner.query(`DROP TABLE "designs"`);
    }

}
