import { MigrationInterface, QueryRunner } from "typeorm";

export class CheckingItems1721674687318 implements MigrationInterface {
    name = 'CheckingItems1721674687318'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "products" ADD "checking_time" TIMESTAMP`);
        await queryRunner.query(`ALTER TABLE "products" ADD "checked" boolean NOT NULL DEFAULT false`);
        await queryRunner.query(`CREATE INDEX "IDX_59713920e40f5663b4e033b7fe" ON "products" ("checking_time") `);
        await queryRunner.query(`CREATE INDEX "IDX_3c6fe611f0eb3a9b8ef5f3385b" ON "products" ("checked") `);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`DROP INDEX "public"."IDX_3c6fe611f0eb3a9b8ef5f3385b"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_59713920e40f5663b4e033b7fe"`);
        await queryRunner.query(`ALTER TABLE "products" DROP COLUMN "checked"`);
        await queryRunner.query(`ALTER TABLE "products" DROP COLUMN "checking_time"`);
    }

}
