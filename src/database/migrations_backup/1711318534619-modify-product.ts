import { MigrationInterface, QueryRunner } from "typeorm";

export class ModifyProduct1711318534619 implements MigrationInterface {
    name = 'ModifyProduct1711318534619'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "products" ADD "design" character varying`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "products" DROP COLUMN "design"`);
    }

}
