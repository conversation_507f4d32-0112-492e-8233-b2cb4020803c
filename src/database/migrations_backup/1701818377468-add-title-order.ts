import { type MigrationInterface, type QueryRunner } from 'typeorm';

export class AddTitleOrder1701818377468 implements MigrationInterface {
  name = 'AddTitleOrder1701818377468';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "orders" ADD "title" character varying`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "orders" DROP COLUMN "title"`);
  }
}
