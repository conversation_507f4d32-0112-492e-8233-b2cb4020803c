import { MigrationInterface, QueryRunner } from "typeorm";

export class ModifyProduct1707511757580 implements MigrationInterface {
    name = 'ModifyProduct1707511757580'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "products" ALTER COLUMN "amazon_quantity" SET DEFAULT '0'`);
        await queryRunner.query(`ALTER TABLE "products" ALTER COLUMN "etsy_quantity" SET DEFAULT '0'`);
        await queryRunner.query(`ALTER TABLE "products" ALTER COLUMN "shopify_quantity" SET DEFAULT '0'`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "products" ALTER COLUMN "shopify_quantity" DROP DEFAULT`);
        await queryRunner.query(`ALTER TABLE "products" ALTER COLUMN "etsy_quantity" DROP DEFAULT`);
        await queryRunner.query(`ALTER TABLE "products" ALTER COLUMN "amazon_quantity" DROP DEFAULT`);
    }

}
