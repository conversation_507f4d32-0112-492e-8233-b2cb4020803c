import { MigrationInterface, QueryRunner } from "typeorm";

export class ModifyProducts1711583780830 implements MigrationInterface {
    name = 'ModifyProducts1711583780830'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "product-design" DROP CONSTRAINT "FK_1e1a59bfdfe69ff41ad71c2fda8"`);
        await queryRunner.query(`ALTER TABLE "product-design" DROP COLUMN "design_id"`);
        await queryRunner.query(`ALTER TABLE "product-design" DROP COLUMN "design_sku"`);
        await queryRunner.query(`ALTER TABLE "product-design" ADD "product_id" uuid`);
        await queryRunner.query(`ALTER TABLE "product-design" ADD "product_sku" character varying`);
        await queryRunner.query(`ALTER TABLE "product-design" ADD CONSTRAINT "FK_61d81805220b9b7a9ef8d02925c" FOREIGN KEY ("product_id", "product_sku") REFERENCES "products"("id","sku") ON DELETE NO ACTION ON UPDATE NO ACTION`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "product-design" DROP CONSTRAINT "FK_61d81805220b9b7a9ef8d02925c"`);
        await queryRunner.query(`ALTER TABLE "product-design" DROP COLUMN "product_sku"`);
        await queryRunner.query(`ALTER TABLE "product-design" DROP COLUMN "product_id"`);
        await queryRunner.query(`ALTER TABLE "product-design" ADD "design_sku" character varying`);
        await queryRunner.query(`ALTER TABLE "product-design" ADD "design_id" uuid`);
        await queryRunner.query(`ALTER TABLE "product-design" ADD CONSTRAINT "FK_1e1a59bfdfe69ff41ad71c2fda8" FOREIGN KEY ("design_id", "design_sku") REFERENCES "products"("id","sku") ON DELETE NO ACTION ON UPDATE NO ACTION`);
    }

}
