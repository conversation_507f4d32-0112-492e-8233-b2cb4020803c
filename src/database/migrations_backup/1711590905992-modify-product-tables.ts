import { MigrationInterface, QueryRunner } from "typeorm";

export class ModifyProductTables1711590905992 implements MigrationInterface {
    name = 'ModifyProductTables1711590905992'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "designs" DROP COLUMN "design"`);
        await queryRunner.query(`ALTER TABLE "designs" ADD "design" character varying NOT NULL`);
        await queryRunner.query(`ALTER TABLE "designs" ADD "title" character varying NOT NULL DEFAULT ''`);
        await queryRunner.query(`ALTER TABLE "designs" ADD "quantity" integer NOT NULL DEFAULT '0'`);
        await queryRunner.query(`ALTER TABLE "designs" ADD "product_id" uuid`);
        await queryRunner.query(`ALTER TABLE "designs" ADD "product_sku" character varying`);
        await queryRunner.query(`ALTER TABLE "designs" ADD CONSTRAINT "FK_ed0b1f5a882a9a2080ef0c51d5e" FOREIGN KEY ("product_id", "product_sku") REFERENCES "products"("id","sku") ON DELETE NO ACTION ON UPDATE NO ACTION`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "designs" DROP CONSTRAINT "FK_ed0b1f5a882a9a2080ef0c51d5e"`);
        await queryRunner.query(`ALTER TABLE "designs" DROP COLUMN "product_sku"`);
        await queryRunner.query(`ALTER TABLE "designs" DROP COLUMN "product_id"`);
        await queryRunner.query(`ALTER TABLE "designs" DROP COLUMN "quantity"`);
        await queryRunner.query(`ALTER TABLE "designs" DROP COLUMN "title"`);
        await queryRunner.query(`ALTER TABLE "designs" DROP COLUMN "design"`);
        await queryRunner.query(`ALTER TABLE "designs" ADD "design" character varying NOT NULL`);
    }

}
