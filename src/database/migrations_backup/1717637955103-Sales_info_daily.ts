import { MigrationInterface, QueryRunner } from "typeorm";

export class SalesInfoDaily1717637955103 implements MigrationInterface {
    name = 'SalesInfoDaily1717637955103'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`CREATE TABLE "sold_products" ("id" SERIAL NOT NULL, "style" character varying(20) NOT NULL, "size" integer NOT NULL, "color" character varying(50) NOT NULL, "fbm_sales" integer NOT NULL, "shopify_sales" integer NOT NULL, "etsy_sales" integer NOT NULL, "total_sales" integer NOT NULL, "local_inventory" integer NOT NULL, "inventory_item_id" character varying(30) NOT NULL, "external_product_id" character varying(30) NOT NULL, "sales_date" TIMESTAMP NOT NULL, CONSTRAINT "PK_c74b7aa8457a96c397204a53074" PRIMARY KEY ("id"))`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`DROP TABLE "sold_products"`);
    }

}
