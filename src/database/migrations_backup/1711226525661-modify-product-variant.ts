import { MigrationInterface, QueryRunner } from "typeorm";

export class ModifyProductVariant1711226525661 implements MigrationInterface {
    name = 'ModifyProductVariant1711226525661'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "products" DROP COLUMN "variant_id"`);
        await queryRunner.query(`ALTER TABLE "products" DROP COLUMN "product_id"`);
        await queryRunner.query(`ALTER TABLE "products" ADD "inventory_item_id" character varying`);
        await queryRunner.query(`ALTER TABLE "products" ADD "inventory_management" character varying`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "products" DROP COLUMN "inventory_management"`);
        await queryRunner.query(`ALTER TABLE "products" DROP COLUMN "inventory_item_id"`);
        await queryRunner.query(`ALTER TABLE "products" ADD "product_id" character varying`);
        await queryRunner.query(`ALTER TABLE "products" ADD "variant_id" character varying`);
    }

}
