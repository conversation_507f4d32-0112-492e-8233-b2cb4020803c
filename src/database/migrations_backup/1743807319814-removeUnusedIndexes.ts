import { MigrationInterface, QueryRunner } from "typeorm";

export class RemoveUnusedIndexes1743807319814 implements MigrationInterface {
    name = 'RemoveUnusedIndexes1743807319814'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`DROP INDEX "public"."IDX_9cfb2b575670a5fc5a62f285b7"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_647c3c34cd53ae65f0bdd2a0a6"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_59713920e40f5663b4e033b7fe"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_3c6fe611f0eb3a9b8ef5f3385b"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_f490ae5677e54abb97fd128012"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_133d88f4cd269ade8dc392132f"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_2108d89eb00bd805ba1ec7d192"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_6a55340156adfa2694a16f04e6"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_36a81fdc8649ae0187256b61ea"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_401610a4dc1f0b4423908319be"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_1902d5541659026b76d1281937"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_1846199852a695713b1f8f5e9a"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_c44ac33a05b144dd0d9ddcf932"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_bf0cb6879f1e567d795c18fa4f"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_f0122d2d1c4cc0d6c5262863f6"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_9b5557f97c61ec0e5710f0d335"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_f27a1985325f30f75735141f1b"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_8f8c69d951dbae5c821835a03a"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_4a585c16ff29e9e814beb15bd8"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_899c793a30cdd11606c3e5b139"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_45f8fb2fafa7b1ee0c6bb73059"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_be2d8b15a3ae31273a8f1062f5"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_ca8c550c9a7e41f2a46cca2ccc"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_f6a2e3115948e406856e2ecc80"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_6c30fbec2fc072b43c9a7a42bb"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_46637f3e991b91afd9d0bcd26f"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_5b64e41ea3df4308f98d903f8c"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_d71582f28a43970104cc8bc76e"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_72830378fa69d60f1e2adf0572"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_7ae58b3a0ca453178eadf2ffd3"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_551696cedf12f15b33b454ba1f"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_403e21e97e28eacdd2d4e42c42"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_7bebd54c2b0de3d9f703a02a74"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_2ca5ffb9d4b7c5eb9054df21aa"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_b3c07d887fce1b2d01d1bd46bc"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_a4757771feba300181109ef636"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_a9bd5cccdb6ebd606b316d985b"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_c7b5cc780cac8baa5465eeee95"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_4c2e552937b7c1c74ae2f6eb10"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_75eba1c6b1a66b09f2a97e6927"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_cad55b3cb25b38be94d2ce831d"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_b953fb29e73df1600c4641c55b"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_a3f1f051c215792b44297aa255"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_41c96d2ed653063b2ecf5f4834"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_80b4e20f0811871df6aa0dcc24"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_da6171090496dc58778421feb7"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_9a54144019c00ce1264b7a3d38"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_a4397bc01866ab48ff5179afe4"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_40ce179e66b3ffb0dc6932a8c9"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_3c658898252e3694655de8a07e"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_57563203a2f728d9b164941035"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_eb5c5063c1274699dc315aa076"`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`CREATE INDEX "IDX_eb5c5063c1274699dc315aa076" ON "colors" ("color") `);
        await queryRunner.query(`CREATE INDEX "IDX_57563203a2f728d9b164941035" ON "color_names" ("value") `);
        await queryRunner.query(`CREATE INDEX "IDX_3c658898252e3694655de8a07e" ON "configurations" ("key") `);
        await queryRunner.query(`CREATE INDEX "IDX_40ce179e66b3ffb0dc6932a8c9" ON "orders" ("quantity") `);
        await queryRunner.query(`CREATE INDEX "IDX_a4397bc01866ab48ff5179afe4" ON "orders" ("color") `);
        await queryRunner.query(`CREATE INDEX "IDX_9a54144019c00ce1264b7a3d38" ON "orders" ("size") `);
        await queryRunner.query(`CREATE INDEX "IDX_da6171090496dc58778421feb7" ON "orders" ("inside") `);
        await queryRunner.query(`CREATE INDEX "IDX_80b4e20f0811871df6aa0dcc24" ON "orders" ("style") `);
        await queryRunner.query(`CREATE INDEX "IDX_41c96d2ed653063b2ecf5f4834" ON "orders" ("engrave_order") `);
        await queryRunner.query(`CREATE INDEX "IDX_a3f1f051c215792b44297aa255" ON "orders" ("sales_channel") `);
        await queryRunner.query(`CREATE INDEX "IDX_b953fb29e73df1600c4641c55b" ON "orders" ("fulfillment_channel") `);
        await queryRunner.query(`CREATE INDEX "IDX_cad55b3cb25b38be94d2ce831d" ON "orders" ("order_id") `);
        await queryRunner.query(`CREATE INDEX "IDX_75eba1c6b1a66b09f2a97e6927" ON "orders" ("order_number") `);
        await queryRunner.query(`CREATE INDEX "IDX_4c2e552937b7c1c74ae2f6eb10" ON "orders" ("price") `);
        await queryRunner.query(`CREATE INDEX "IDX_c7b5cc780cac8baa5465eeee95" ON "orders" ("source") `);
        await queryRunner.query(`CREATE INDEX "IDX_a9bd5cccdb6ebd606b316d985b" ON "orders" ("external_created_at") `);
        await queryRunner.query(`CREATE INDEX "IDX_a4757771feba300181109ef636" ON "orders" ("external_order_id") `);
        await queryRunner.query(`CREATE INDEX "IDX_b3c07d887fce1b2d01d1bd46bc" ON "orders" ("outside") `);
        await queryRunner.query(`CREATE INDEX "IDX_2ca5ffb9d4b7c5eb9054df21aa" ON "orders" ("item_no") `);
        await queryRunner.query(`CREATE INDEX "IDX_7bebd54c2b0de3d9f703a02a74" ON "designs" ("design") `);
        await queryRunner.query(`CREATE INDEX "IDX_403e21e97e28eacdd2d4e42c42" ON "design_names" ("value") `);
        await queryRunner.query(`CREATE INDEX "IDX_551696cedf12f15b33b454ba1f" ON "sold_products" ("saved") `);
        await queryRunner.query(`CREATE INDEX "IDX_7ae58b3a0ca453178eadf2ffd3" ON "sold_products" ("sales_date") `);
        await queryRunner.query(`CREATE INDEX "IDX_72830378fa69d60f1e2adf0572" ON "sold_products" ("external_product_id") `);
        await queryRunner.query(`CREATE INDEX "IDX_d71582f28a43970104cc8bc76e" ON "sold_products" ("inventory_item_id") `);
        await queryRunner.query(`CREATE INDEX "IDX_5b64e41ea3df4308f98d903f8c" ON "sold_products" ("total_sales") `);
        await queryRunner.query(`CREATE INDEX "IDX_46637f3e991b91afd9d0bcd26f" ON "sold_products" ("etsy_sales") `);
        await queryRunner.query(`CREATE INDEX "IDX_6c30fbec2fc072b43c9a7a42bb" ON "sold_products" ("shopify_sales") `);
        await queryRunner.query(`CREATE INDEX "IDX_f6a2e3115948e406856e2ecc80" ON "sold_products" ("fbm_sales") `);
        await queryRunner.query(`CREATE INDEX "IDX_ca8c550c9a7e41f2a46cca2ccc" ON "sold_products" ("color") `);
        await queryRunner.query(`CREATE INDEX "IDX_be2d8b15a3ae31273a8f1062f5" ON "sold_products" ("style") `);
        await queryRunner.query(`CREATE INDEX "IDX_45f8fb2fafa7b1ee0c6bb73059" ON "products" ("shopify_quantity") `);
        await queryRunner.query(`CREATE INDEX "IDX_899c793a30cdd11606c3e5b139" ON "products" ("etsy_quantity") `);
        await queryRunner.query(`CREATE INDEX "IDX_4a585c16ff29e9e814beb15bd8" ON "products" ("amazon_quantity") `);
        await queryRunner.query(`CREATE INDEX "IDX_8f8c69d951dbae5c821835a03a" ON "products" ("quantity") `);
        await queryRunner.query(`CREATE INDEX "IDX_f27a1985325f30f75735141f1b" ON "products" ("size") `);
        await queryRunner.query(`CREATE INDEX "IDX_9b5557f97c61ec0e5710f0d335" ON "products" ("design") `);
        await queryRunner.query(`CREATE INDEX "IDX_f0122d2d1c4cc0d6c5262863f6" ON "products" ("style") `);
        await queryRunner.query(`CREATE INDEX "IDX_bf0cb6879f1e567d795c18fa4f" ON "products" ("color") `);
        await queryRunner.query(`CREATE INDEX "IDX_c44ac33a05b144dd0d9ddcf932" ON "products" ("sku") `);
        await queryRunner.query(`CREATE INDEX "IDX_1846199852a695713b1f8f5e9a" ON "products" ("status") `);
        await queryRunner.query(`CREATE INDEX "IDX_1902d5541659026b76d1281937" ON "products" ("inventory_management") `);
        await queryRunner.query(`CREATE INDEX "IDX_401610a4dc1f0b4423908319be" ON "products" ("inventory_item_id") `);
        await queryRunner.query(`CREATE INDEX "IDX_36a81fdc8649ae0187256b61ea" ON "products" ("source") `);
        await queryRunner.query(`CREATE INDEX "IDX_6a55340156adfa2694a16f04e6" ON "products" ("external_created_at") `);
        await queryRunner.query(`CREATE INDEX "IDX_2108d89eb00bd805ba1ec7d192" ON "products" ("external_product_id") `);
        await queryRunner.query(`CREATE INDEX "IDX_133d88f4cd269ade8dc392132f" ON "products" ("asin") `);
        await queryRunner.query(`CREATE INDEX "IDX_f490ae5677e54abb97fd128012" ON "products" ("fnsku") `);
        await queryRunner.query(`CREATE INDEX "IDX_3c6fe611f0eb3a9b8ef5f3385b" ON "products" ("checked") `);
        await queryRunner.query(`CREATE INDEX "IDX_59713920e40f5663b4e033b7fe" ON "products" ("checking_time") `);
        await queryRunner.query(`CREATE INDEX "IDX_647c3c34cd53ae65f0bdd2a0a6" ON "product_designs" ("quantity") `);
        await queryRunner.query(`CREATE INDEX "IDX_9cfb2b575670a5fc5a62f285b7" ON "product_designs" ("title") `);
    }

}
