import { MigrationInterface, QueryRunner } from "typeorm";

export class ModifyProducts1711580997885 implements MigrationInterface {
    name = 'ModifyProducts1711580997885'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`CREATE TABLE "product-design" ("id" SERIAL NOT NULL, "title" character varying NOT NULL DEFAULT '', "quantity" integer NOT NULL DEFAULT '0', "design_id" uuid, "design_sku" character varying, CONSTRAINT "PK_0bf007b1267bb5032499f7b09ce" PRIMARY KEY ("id"))`);
        await queryRunner.query(`ALTER TABLE "product-design" ADD CONSTRAINT "FK_1e1a59bfdfe69ff41ad71c2fda8" FOREIGN KEY ("design_id", "design_sku") REFERENCES "products"("id","sku") ON DELETE NO ACTION ON UPDATE NO ACTION`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "product-design" DROP CONSTRAINT "FK_1e1a59bfdfe69ff41ad71c2fda8"`);
        await queryRunner.query(`DROP TABLE "product-design"`);
    }

}
