import { type MigrationInterface, type QueryRunner } from 'typeorm';

export class CreateProductsTable1700508817968 implements MigrationInterface {
  name = 'CreateProductsTable1700508817968';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `CREATE TABLE "products" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "created_at" TIMESTAMP NOT NULL DEFAULT now(), "updated_at" TIMESTAMP NOT NULL DEFAULT now(), "external_product_id" text NOT NULL, "external_created_at" TIMESTAMP NOT NULL, "data_json" text NOT NULL, "source" character varying NOT NULL DEFAULT 'shopify', "hash" character varying NOT NULL, "title" character varying NOT NULL, "status" character varying NOT NULL, "quantity" integer NOT NULL, CONSTRAINT "PK_0806c755e0aca124e67c0cf6d7d" PRIMARY KEY ("id"))`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`DROP TABLE "products"`);
  }
}
