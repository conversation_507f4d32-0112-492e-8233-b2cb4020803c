import { MigrationInterface, QueryRunner } from "typeorm";

export class AddVariationOosProduct1711223193755 implements MigrationInterface {
    name = 'AddVariationOosProduct1711223193755'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "products" ADD "oos_date" TIMESTAMP`);
        await queryRunner.query(`ALTER TABLE "products" ADD "variant_id" character varying`);
        await queryRunner.query(`ALTER TABLE "products" ADD "product_id" character varying`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "products" DROP COLUMN "product_id"`);
        await queryRunner.query(`ALTER TABLE "products" DROP COLUMN "variant_id"`);
        await queryRunner.query(`ALTER TABLE "products" DROP COLUMN "oos_date"`);
    }

}
