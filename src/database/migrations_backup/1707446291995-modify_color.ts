import { MigrationInterface, QueryRunner } from "typeorm";

export class ModifyColor1707446291995 implements MigrationInterface {
    name = 'ModifyColor1707446291995'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "colors" ADD "code" character varying`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "colors" DROP COLUMN "code"`);
    }

}
