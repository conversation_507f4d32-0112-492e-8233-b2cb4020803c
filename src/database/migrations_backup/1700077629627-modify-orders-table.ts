import { MigrationInterface, QueryRunner } from 'typeorm';

export class ModifyOrdersTable1700077629627 implements MigrationInterface {
  name = 'ModifyOrdersTable1700077629627';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "orders" DROP COLUMN "external_order_id"`,
    );
    await queryRunner.query(
      `ALTER TABLE "orders" DROP COLUMN "external_created_at"`,
    );
    await queryRunner.query(
      `ALTER TABLE "orders" DROP COLUMN "external_price"`,
    );
    await queryRunner.query(
      `ALTER TABLE "orders" DROP COLUMN "external_currency"`,
    );
    await queryRunner.query(`ALTER TABLE "orders" DROP COLUMN "status"`);
    await queryRunner.query(
      `ALTER TABLE "orders" DROP COLUMN "financial_status"`,
    );
    await queryRunner.query(`ALTER TABLE "orders" DROP COLUMN "name"`);
    await queryRunner.query(
      `ALTER TABLE "orders" ADD "order_id" character varying NOT NULL`,
    );
    await queryRunner.query(
      `ALTER TABLE "orders" ADD "message" character varying NOT NULL`,
    );
    await queryRunner.query(
      `ALTER TABLE "orders" ADD "printed" boolean NOT NULL DEFAULT false`,
    );
    await queryRunner.query(
      `ALTER TABLE "orders" ADD "engrave_order" boolean NOT NULL DEFAULT false`,
    );
    await queryRunner.query(
      `ALTER TABLE "orders" ADD "shipped" boolean NOT NULL DEFAULT false`,
    );
    await queryRunner.query(
      `ALTER TABLE "orders" ADD "ready_to_print" boolean NOT NULL DEFAULT false`,
    );
    await queryRunner.query(
      `ALTER TABLE "orders" ADD "customer_name" character varying NOT NULL`,
    );
    await queryRunner.query(
      `ALTER TABLE "orders" ADD "style" character varying NOT NULL`,
    );
    await queryRunner.query(
      `ALTER TABLE "orders" ADD "item_no" character varying NOT NULL`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "orders" DROP COLUMN "item_no"`);
    await queryRunner.query(`ALTER TABLE "orders" DROP COLUMN "style"`);
    await queryRunner.query(`ALTER TABLE "orders" DROP COLUMN "customer_name"`);
    await queryRunner.query(
      `ALTER TABLE "orders" DROP COLUMN "ready_to_print"`,
    );
    await queryRunner.query(`ALTER TABLE "orders" DROP COLUMN "shipped"`);
    await queryRunner.query(`ALTER TABLE "orders" DROP COLUMN "engrave_order"`);
    await queryRunner.query(`ALTER TABLE "orders" DROP COLUMN "printed"`);
    await queryRunner.query(`ALTER TABLE "orders" DROP COLUMN "message"`);
    await queryRunner.query(`ALTER TABLE "orders" DROP COLUMN "order_id"`);
    await queryRunner.query(
      `ALTER TABLE "orders" ADD "name" character varying NOT NULL`,
    );
    await queryRunner.query(
      `ALTER TABLE "orders" ADD "financial_status" character varying NOT NULL`,
    );
    await queryRunner.query(
      `ALTER TABLE "orders" ADD "status" character varying NOT NULL DEFAULT 'initiated'`,
    );
    await queryRunner.query(
      `ALTER TABLE "orders" ADD "external_currency" character varying NOT NULL`,
    );
    await queryRunner.query(
      `ALTER TABLE "orders" ADD "external_price" numeric NOT NULL`,
    );
    await queryRunner.query(
      `ALTER TABLE "orders" ADD "external_created_at" TIMESTAMP NOT NULL`,
    );
    await queryRunner.query(
      `ALTER TABLE "orders" ADD "external_order_id" text NOT NULL`,
    );
  }
}
