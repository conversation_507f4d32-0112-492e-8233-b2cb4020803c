import { MigrationInterface, QueryRunner } from "typeorm";

export class ModifyDesignTable1707510807530 implements MigrationInterface {
    name = 'ModifyDesignTable1707510807530'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "designs" DROP COLUMN "code"`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "designs" ADD "code" character varying`);
    }

}
