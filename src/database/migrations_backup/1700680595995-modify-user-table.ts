import { MigrationInterface, QueryRunner } from "typeorm";

export class ModifyUserTable1700680595995 implements MigrationInterface {
    name = 'ModifyUserTable1700680595995'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "user_settings" DROP COLUMN "is_phone_verified"`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "user_settings" ADD "is_phone_verified" boolean NOT NULL DEFAULT false`);
    }

}
