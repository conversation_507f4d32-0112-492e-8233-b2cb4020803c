import { MigrationInterface, QueryRunner } from "typeorm";

export class ModifyProduct1711590176734 implements MigrationInterface {
    name = 'ModifyProduct1711590176734'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "products" DROP COLUMN "title"`);
        await queryRunner.query(`ALTER TABLE "products" ALTER COLUMN "sku" SET DEFAULT ''`);
        await queryRunner.query(`ALTER TABLE "products" ALTER COLUMN "quantity" SET NOT NULL`);
        await queryRunner.query(`ALTER TABLE "products" ALTER COLUMN "amazon_quantity" SET NOT NULL`);
        await queryRunner.query(`ALTER TABLE "products" ALTER COLUMN "etsy_quantity" SET NOT NULL`);
        await queryRunner.query(`ALTER TABLE "products" ALTER COLUMN "shopify_quantity" SET NOT NULL`);
        await queryRunner.query(`ALTER TABLE "products" DROP COLUMN "note"`);
        await queryRunner.query(`ALTER TABLE "products" ADD "note" character varying`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "products" DROP COLUMN "note"`);
        await queryRunner.query(`ALTER TABLE "products" ADD "note" text`);
        await queryRunner.query(`ALTER TABLE "products" ALTER COLUMN "shopify_quantity" DROP NOT NULL`);
        await queryRunner.query(`ALTER TABLE "products" ALTER COLUMN "etsy_quantity" DROP NOT NULL`);
        await queryRunner.query(`ALTER TABLE "products" ALTER COLUMN "amazon_quantity" DROP NOT NULL`);
        await queryRunner.query(`ALTER TABLE "products" ALTER COLUMN "quantity" DROP NOT NULL`);
        await queryRunner.query(`ALTER TABLE "products" ALTER COLUMN "sku" SET DEFAULT '-'`);
        await queryRunner.query(`ALTER TABLE "products" ADD "title" character varying NOT NULL`);
    }

}
