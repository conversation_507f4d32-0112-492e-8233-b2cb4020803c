import { MigrationInterface, QueryRunner } from "typeorm";

export class ModifyProductTable1700514984073 implements MigrationInterface {
    name = 'ModifyProductTable1700514984073'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "products" ADD "color" character varying NOT NULL`);
        await queryRunner.query(`ALTER TABLE "products" ADD "style" character varying NOT NULL`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "products" DROP COLUMN "style"`);
        await queryRunner.query(`ALTER TABLE "products" DROP COLUMN "color"`);
    }

}
