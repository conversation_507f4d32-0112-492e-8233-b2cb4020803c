import { type MigrationInterface, type QueryRunner } from 'typeorm';

export class UpdateOrdersTable1699740715160 implements MigrationInterface {
  name = 'UpdateOrdersTable1699740715160';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "orders" ADD "current_subtotal_price" numeric NOT NULL`,
    );
    await queryRunner.query(
      `ALTER TABLE "orders" ADD "order_number" integer NOT NULL`,
    );
    await queryRunner.query(
      `ALTER TABLE "orders" ADD "email" character varying NOT NULL`,
    );
    await queryRunner.query(
      `ALTER TABLE "orders" ADD "financial_status" character varying NOT NULL`,
    );
    await queryRunner.query(
      `ALTER TABLE "orders" ADD "name" character varying NOT NULL`,
    );
    await queryRunner.query(
      `ALTER TABLE "orders" ADD "note" character varying NOT NULL`,
    );
    await queryRunner.query(
      `ALTER TABLE "orders" ADD "outside" character varying NOT NULL`,
    );
    await queryRunner.query(
      `ALTER TABLE "orders" ADD "inside" character varying NOT NULL`,
    );
    await queryRunner.query(
      `ALTER TABLE "orders" ADD "gold_inlay" character varying NOT NULL`,
    );
    await queryRunner.query(`ALTER TABLE "orders" ADD "size" integer NOT NULL`);
    await queryRunner.query(
      `ALTER TABLE "orders" ADD "color" character varying NOT NULL`,
    );
    await queryRunner.query(
      `ALTER TABLE "orders" ADD "sku" character varying NOT NULL`,
    );
    await queryRunner.query(
      `ALTER TABLE "orders" ADD "quantity" integer NOT NULL`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "orders" DROP COLUMN "quantity"`);
    await queryRunner.query(`ALTER TABLE "orders" DROP COLUMN "sku"`);
    await queryRunner.query(`ALTER TABLE "orders" DROP COLUMN "color"`);
    await queryRunner.query(`ALTER TABLE "orders" DROP COLUMN "size"`);
    await queryRunner.query(`ALTER TABLE "orders" DROP COLUMN "gold_inlay"`);
    await queryRunner.query(`ALTER TABLE "orders" DROP COLUMN "inside"`);
    await queryRunner.query(`ALTER TABLE "orders" DROP COLUMN "outside"`);
    await queryRunner.query(`ALTER TABLE "orders" DROP COLUMN "note"`);
    await queryRunner.query(`ALTER TABLE "orders" DROP COLUMN "name"`);
    await queryRunner.query(
      `ALTER TABLE "orders" DROP COLUMN "financial_status"`,
    );
    await queryRunner.query(`ALTER TABLE "orders" DROP COLUMN "email"`);
    await queryRunner.query(`ALTER TABLE "orders" DROP COLUMN "order_number"`);
    await queryRunner.query(
      `ALTER TABLE "orders" DROP COLUMN "current_subtotal_price"`,
    );
  }
}
