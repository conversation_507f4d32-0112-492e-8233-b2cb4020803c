import { type MigrationInterface, type QueryRunner } from 'typeorm';

export class AddPriceSingleItem1701818088677 implements MigrationInterface {
  name = 'AddPriceSingleItem1701818088677';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "orders" ADD "price" numeric`);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "orders" DROP COLUMN "price"`);
  }
}
