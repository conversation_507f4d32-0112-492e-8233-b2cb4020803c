import { MigrationInterface, QueryRunner } from "typeorm";

export class ModifyProducts1711585432176 implements MigrationInterface {
    name = 'ModifyProducts1711585432176'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "product-design" DROP CONSTRAINT "FK_61d81805220b9b7a9ef8d02925c"`);
        await queryRunner.query(`ALTER TABLE "products" ALTER COLUMN "sku" SET DEFAULT '-'`);
        await queryRunner.query(`ALTER TABLE "product-design" ADD CONSTRAINT "FK_61d81805220b9b7a9ef8d02925c" FOREIGN KEY ("product_id", "product_sku") REFERENCES "products"("id","sku") ON DELETE NO ACTION ON UPDATE NO ACTION`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "product-design" DROP CONSTRAINT "FK_61d81805220b9b7a9ef8d02925c"`);
        await queryRunner.query(`ALTER TABLE "products" ALTER COLUMN "sku" DROP DEFAULT`);
        await queryRunner.query(`ALTER TABLE "product-design" ADD CONSTRAINT "FK_61d81805220b9b7a9ef8d02925c" FOREIGN KEY ("product_id", "product_sku") REFERENCES "products"("id","sku") ON DELETE NO ACTION ON UPDATE NO ACTION`);
    }

}
