import { MigrationInterface, QueryRunner } from "typeorm";

export class UpdateEntities1709948115335 implements MigrationInterface {
    name = 'UpdateEntities1709948115335'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "orders" DROP COLUMN "data_json"`);
        await queryRunner.query(`ALTER TABLE "orders" DROP COLUMN "hash"`);
        await queryRunner.query(`ALTER TABLE "orders" DROP COLUMN "printed"`);
        await queryRunner.query(`ALTER TABLE "orders" DROP COLUMN "shipped"`);
        await queryRunner.query(`ALTER TABLE "orders" DROP COLUMN "ready_to_print"`);
        await queryRunner.query(`ALTER TABLE "products" DROP COLUMN "data_json"`);
        await queryRunner.query(`ALTER TABLE "products" DROP COLUMN "hash"`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "products" ADD "hash" character varying NOT NULL`);
        await queryRunner.query(`ALTER TABLE "products" ADD "data_json" text NOT NULL`);
        await queryRunner.query(`ALTER TABLE "orders" ADD "ready_to_print" boolean NOT NULL DEFAULT false`);
        await queryRunner.query(`ALTER TABLE "orders" ADD "shipped" boolean NOT NULL DEFAULT false`);
        await queryRunner.query(`ALTER TABLE "orders" ADD "printed" boolean NOT NULL DEFAULT false`);
        await queryRunner.query(`ALTER TABLE "orders" ADD "hash" character varying NOT NULL`);
        await queryRunner.query(`ALTER TABLE "orders" ADD "data_json" text NOT NULL`);
    }

}
