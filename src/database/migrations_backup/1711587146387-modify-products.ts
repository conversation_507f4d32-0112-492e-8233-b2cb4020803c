import { MigrationInterface, QueryRunner } from "typeorm";

export class ModifyProducts1711587146387 implements MigrationInterface {
    name = 'ModifyProducts1711587146387'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "products" DROP COLUMN "external_created_at"`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "products" ADD "external_created_at" TIMESTAMP NOT NULL`);
    }

}
