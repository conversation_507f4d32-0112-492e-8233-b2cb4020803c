import { MigrationInterface, QueryRunner } from "typeorm";

export class AddSavedVakue1721083777552 implements MigrationInterface {
    name = 'AddSavedVakue1721083777552'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "sold_products" ADD "saved" boolean NOT NULL DEFAULT false`);
        await queryRunner.query(`CREATE INDEX "IDX_551696cedf12f15b33b454ba1f" ON "sold_products" ("saved") `);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`DROP INDEX "public"."IDX_551696cedf12f15b33b454ba1f"`);
        await queryRunner.query(`ALTER TABLE "sold_products" DROP COLUMN "saved"`);
    }

}
