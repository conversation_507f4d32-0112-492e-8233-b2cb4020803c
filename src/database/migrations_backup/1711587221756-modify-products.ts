import { MigrationInterface, QueryRunner } from "typeorm";

export class ModifyProducts1711587221756 implements MigrationInterface {
    name = 'ModifyProducts1711587221756'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "products" ALTER COLUMN "status" SET DEFAULT ''`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "products" ALTER COLUMN "status" DROP DEFAULT`);
    }

}
