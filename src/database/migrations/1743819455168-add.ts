import { MigrationInterface, QueryRunner } from "typeorm";

export class Add1743819455168 implements MigrationInterface {
    name = 'Add1743819455168'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "products" ADD "title" character varying NOT NULL DEFAULT '-'`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "products" DROP COLUMN "title"`);
    }

}
