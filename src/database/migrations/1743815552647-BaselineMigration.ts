import { MigrationInterface, QueryRunner } from 'typeorm';

export class BaselineMigration1743815552647 implements MigrationInterface {
  name = 'BaselineMigration1743815552647';

  public async up(queryRunner: QueryRunner): Promise<void> {
    // Record this migration in the migrations table
    await queryRunner.query(`
            INSERT INTO migrations (timestamp, name)
            VALUES (1743815552647, 'BaselineMigration1743815552647')
        `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // Remove this migration from the migrations table
    await queryRunner.query(`
            DELETE FROM migrations
            WHERE name = 'BaselineMigration1743815552647'
        `);
  }
}
