import { Entity, Column, PrimaryGeneratedColumn } from 'typeorm';

@Entity({ name: 'sold_products' })
export class SoldProductEntity {
  @PrimaryGeneratedColumn()
  id: number = 0;

  @Column({ type: 'varchar', length: 20 })
  style: string = '';

  @Column({ type: 'int' })
  size: number = 0;

  @Column({ type: 'varchar', length: 50 })
  color: string = '';

  @Column({ type: 'int' })
  fbm_sales: number = 0;

  @Column({ type: 'int' })
  shopify_sales: number = 0;

  @Column({ type: 'int' })
  etsy_sales: number = 0;

  @Column({ type: 'int' })
  total_sales: number = 0;

  @Column({ type: 'varchar', length: 30 })
  inventory_item_id: string = '';

  @Column({ type: 'varchar', length: 30 })
  external_product_id: string = '';

  @Column({ default: false })
  saved?: boolean;

  @Column()
  salesDate!: Date;
}
