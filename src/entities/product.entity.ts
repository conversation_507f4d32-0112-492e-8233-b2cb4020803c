import { Column, <PERSON>tity, OneToMany, PrimaryColumn } from 'typeorm';

import { AbstractEntity } from '../common/abstract.entity';
import { ProductDesignEntity } from './product-design.entity';
import { UseDto } from '../common/decorators';
import { ProductDto } from '../modules/product/dto/product.dto';

@Entity({ name: 'products' })
@UseDto(ProductDto)
export class ProductEntity extends AbstractEntity<ProductDto> {
  @Column({ default: '-' })
  externalProductId?: string;

  @Column({ default: '-' })
  FNSKU?: string;

  @Column({ default: '-' })
  ASIN?: string;

  @Column({ nullable: true })
  externalCreatedAt!: Date;

  @Column({ type: 'timestamp', nullable: true })
  oosDate?: Date | null;

  @Column({ type: 'timestamp', nullable: true })
  amazonUpdatedTime?: Date | null;

  @Column({ default: 'Shopify' })
  source?: string;

  @Column({ default: '-' })
  title?: string;

  @Column({ nullable: true })
  inventory_item_id?: string;

  @Column({ nullable: true })
  inventory_management?: string;

  @Column({ default: '' })
  status?: string;

  @PrimaryColumn({ default: '' })
  sku?: string;

  @Column({ nullable: true })
  color?: string;

  @Column({ nullable: true })
  style?: string;

  @Column({ nullable: true })
  design?: string;

  @Column({ nullable: true })
  size?: number;

  @Column({ default: 0 })
  quantity?: number;

  @Column({ default: 0 })
  amazon_quantity?: number;

  @Column({ default: 0 })
  etsy_quantity?: number;

  @Column({ type: 'timestamp', nullable: true })
  checkingTime?: Date | null;

  @Column({ default: false })
  checked?: boolean;

  @Column({ default: 0 })
  shopify_quantity?: number;

  @Column({ nullable: true })
  note?: string;

  @Column({ nullable: true })
  amazon_skus?: string;

  @Column({ nullable: true })
  etsy_offerings?: string;

  @OneToMany(() => ProductDesignEntity, (item) => item.product)
  product_designs?: ProductDesignEntity[];
}
