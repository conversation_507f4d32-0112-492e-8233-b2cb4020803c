import { Column, Entity, Index } from 'typeorm';

import { AbstractEntity } from '../common/abstract.entity';
import { OrderDto } from '../modules/orders/dto/order.dto';
import { UseDto } from '../common/decorators';

@Entity({ name: 'orders' })
@UseDto(OrderDto)
export class OrderEntity extends AbstractEntity<OrderDto> {
  @Column({ type: 'text' })
  externalOrderId!: string;

  @Column()
  externalCreatedAt!: Date;

  @Column({ type: 'decimal' })
  externalPrice!: number;

  @Column()
  externalCurrency?: string;

  @Column({ nullable: true })
  title?: string;

  @Column({ nullable: true })
  status?: string;

  @Column({ nullable: true })
  data?: string;

  @Column({ nullable: true })
  city?: string;

  @Column({ nullable: true })
  province?: string;

  @Column({ default: false })
  isMixedOrder?: boolean;

  @Column({ nullable: true })
  country?: string;

  @Column({ default: 'Shopify' })
  source!: string;

  @Column({ type: 'decimal' })
  current_subtotal_price?: number;

  @Column({ type: 'decimal', nullable: true })
  total_discounts?: number;

  @Column({ type: 'decimal', nullable: true })
  total_shipping_price?: number;

  @Column({ type: 'decimal', nullable: true })
  total_tax?: number;

  @Column({ type: 'decimal', nullable: true })
  price?: number;

  @Column()
  order_number!: number;

  @Column({ nullable: true })
  email?: string;

  @Column()
  order_id!: string;

  @Column({ nullable: true })
  note?: string;

  @Column({ nullable: true })
  message?: string;

  @Column({ nullable: true })
  fulfillmentChannel?: string;

  @Column({ nullable: true })
  salesChannel?: string;

  @Column({ default: false })
  engrave_order?: boolean;

  @Column({ default: false })
  saved?: boolean;

  @Column()
  customer_name?: string;

  @Column()
  style?: string;

  @Column()
  item_no?: string;

  @Column({ nullable: true })
  outside?: string;

  @Column({ nullable: true })
  inside?: string;

  @Column({ nullable: true })
  goldInlay?: string;

  @Column()
  size?: number;

  @Column()
  color?: string;

  @Column()
  @Index()
  sku?: string;

  @Column()
  quantity?: number;
}
