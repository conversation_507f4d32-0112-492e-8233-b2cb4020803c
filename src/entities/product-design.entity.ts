import { Entity, PrimaryGeneratedColumn, Column, ManyToOne } from 'typeorm';
import { ProductEntity } from './product.entity';

@Entity('product_designs')
export class ProductDesignEntity {
  @PrimaryGeneratedColumn()
  id!: number;

  @Column({ default: '' })
  title?: string;

  @Column({ default: 0 })
  quantity?: number;

  @ManyToOne(() => ProductEntity, (item) => item.product_designs)
  product?: ProductEntity;
}
