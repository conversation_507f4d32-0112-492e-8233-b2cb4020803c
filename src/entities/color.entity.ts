// src/colors/entities/color.entity.ts
import { Entity, PrimaryGeneratedColumn, Column, OneToMany } from 'typeorm';
import { ColorNameEntity } from './color-name.entity';

@Entity('colors')
export class ColorEntity {
  @PrimaryGeneratedColumn()
  id!: number;

  @Column()
  color!: string;

  @Column({ nullable: true })
  code?: string;

  @OneToMany(() => ColorNameEntity, (colorName) => colorName.color)
  names?: ColorNameEntity[];
}
