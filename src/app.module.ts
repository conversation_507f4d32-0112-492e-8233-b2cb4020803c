import './boilerplate.polyfill';

import { BullModule } from '@nestjs/bull';
import { Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { TypeOrmModule } from '@nestjs/typeorm';

import { ApiConfigService } from './common/services/api-config.service';
import { CacheModule } from '@nestjs/cache-manager';
import { SharedModule } from './common/shared.module';
import { ProductModule } from './modules/product/product.module';
import { ScheduleModule } from '@nestjs/schedule';
import { AuthModule } from './modules/auth/auth.module';
import { DataModule } from './modules/orders/data.module';
import { UserModule } from './modules/user/user.module';

@Module({
  imports: [
    // Load environment variables first
    ConfigModule.forRoot({
      isGlobal: true,
      envFilePath: '.env',
    }),
    SharedModule,

    // Initialize TypeORM early with optimized settings
    TypeOrmModule.forRootAsync({
      useFactory: (configService: ApiConfigService) => ({
        ...configService.postgresConfig,
        autoLoadEntities: true,
        entitySkipConstructor: true,
        lazyRelations: true,
        verboseRetryLog: true,
        maxQueryExecutionTime: 60000,
        retryAttempts: 5,
        retryDelay: 5000,
      }),
      inject: [ApiConfigService],
    }),

    // Initialize other services
    CacheModule.register({
      isGlobal: true,
      host: process.env.REDIS_HOST,
      port: process.env.REDIS_PORT,
      username: process.env.REDIS_USERNAME,
      password: process.env.REDIS_PASSWORD,
      no_ready_check: true,
    }),
    ScheduleModule.forRoot(),
    // Initialize feature modules
    AuthModule,
    UserModule,
    DataModule,
    ProductModule,
    // Initialize bull queue last
    BullModule.forRootAsync({
      inject: [ApiConfigService],
      useFactory: (configService: ApiConfigService) => configService.bullConfig,
    }),
  ],
  providers: [],
})
export class AppModule {}
