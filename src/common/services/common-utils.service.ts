// src/common/services/common-utils.service.ts
import { Injectable, Logger } from '@nestjs/common';
import Axios, { AxiosInstance } from 'axios';

@Injectable()
export class CommonUtilsService {
  private readonly logger = new Logger(CommonUtilsService.name);

  // constructor(private readonly appConfig: ApiConfigService) {}

  createAxiosInstance(
    baseURL: string,
    headers: Record<string, string>,
  ): AxiosInstance {
    return Axios.create({
      baseURL,
      headers,
    });
  }

  logRequestDetails(url: string, params: any) {
    this.logger.error(`Request URL: ${url}`);
    if (params) {
      this.logger.error('Request Params:', params);
    }
  }

  async handleAxiosError(
    error: any,
    retryRequest: (config: any) => Promise<any>,
  ) {
    const { response, config } = error;
    if (response) {
      const { status } = response;
      this.logRequestDetails(config.url, config.params);
      switch (status) {
        case 429:
          this.logger.error('Too many requests, retrying...');
          await new Promise((resolve) => setTimeout(resolve, 5000));
          return retryRequest(config);
        case 503:
          this.logger.error('Service unavailable');
          break;
        default:
          this.logger.error(
            `Error: ${status} - ${JSON.stringify(response.data)}`,
          );
      }
    } else {
      this.logger.error('Network error:', error.message);
    }
    return Promise.reject(error);
  }
}
