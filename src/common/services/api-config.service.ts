/* eslint-disable @typescript-eslint/no-explicit-any */
import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { type TypeOrmModuleOptions } from '@nestjs/typeorm';
import { isNil } from 'lodash';

import { SnakeNamingStrategy } from '../../config/snake-naming.strategy';

@Injectable()
export class ApiConfigService {
  constructor(private configService: ConfigService) {}

  get isDevelopment(): boolean {
    return this.nodeEnv === 'development';
  }

  get isProduction(): boolean {
    return this.nodeEnv === 'production';
  }

  get isTest(): boolean {
    return this.nodeEnv === 'test';
  }

  private getNumber(key: string): number {
    const value = this.get(key);

    try {
      return Number(value);
    } catch {
      throw new Error(key + ' environment variable is not a number');
    }
  }

  private getBoolean(key: string): boolean {
    const value = this.get(key);

    try {
      return Boolean(JSON.parse(value));
    } catch {
      throw new Error(key + ' env var is not a boolean');
    }
  }

  private getString(key: string): string {
    const value = this.get(key);

    return value.replaceAll('\\n', '\n');
  }

  get nodeEnv(): string {
    return this.getString('NODE_ENV');
  }

  get shopifyAPIBaseUrl(): string {
    return this.getString('SHOPIFY_BASE_URL');
  }

  get amazonAPIBaseUrl(): string {
    return this.getString('AMAZON_API_URL');
  }

  get amazonRefreshToken(): string {
    return this.getString('AMAZON_REFRESH_TOKEN');
  }

  get amazonClientId(): string {
    return this.getString('AMAZON_CLIENT_ID');
  }

  get amazonClientSecret(): string {
    return this.getString('AMAZON_CLIENT_SECRET');
  }

  get shopifyToken(): string {
    return this.getString('SHOPIFY_TOKEN');
  }

  get etsyRefreshToken(): string {
    return this.getString('ETSY_REFRESH_TOKEN');
  }

  get etsyRefreshUrl(): string {
    return this.getString('ETSY_REFRESH_URL');
  }

  get etsyShopID(): number {
    return this.getNumber('ETSY_SHOP_ID');
  }

  get etsyApiSecret(): string {
    return this.getString('ETSY_API_SECRET');
  }

  get etsyApiKey(): string {
    return this.getString('ETSY_API_KEY');
  }

  get bullConfig(): Record<any, any> {
    return {
      redis: {
        host: this.getString('REDIS_HOST'),
        port: this.getNumber('REDIS_PORT'),
        db: 2,
        password: this.getString('REDIS_PASSWORD'),
        username: this.getString('REDIS_USERNAME'),
      },
      defaultJobOptions: {
        attempts: 3,
        removeOnComplete: true,
        removeOnFail: 3,
        backoff: {
          type: 'exponential',
          delay: 2000,
        },
      },
      limiter: {
        max: 3,
        duration: 500,
      },
    };
  }

  get postgresConfig(): TypeOrmModuleOptions {
    let entities = [
      __dirname + '/../../modules/**/*.entity{.ts,.js}',
      __dirname + '/../../modules/**/*.view-entity{.ts,.js}',
    ];
    let migrations = [__dirname + '/../../database/migrations/*{.ts,.js}'];

    if (module.hot) {
      const entityContext = require.context(
        './../../modules',
        true,
        /\.entity\.ts$/,
      );
      entities = entityContext.keys().map((id) => {
        const entityModule = entityContext<Record<string, unknown>>(id);
        const [entity] = Object.values(entityModule);

        return entity as string;
      });
      const migrationContext = require.context(
        './../../database/migrations',
        false,
        /\.ts$/,
      );

      migrations = migrationContext.keys().map((id) => {
        const migrationModule = migrationContext<Record<string, unknown>>(id);
        const [migration] = Object.values(migrationModule);

        return migration as string;
      });
    }

    return {
      entities,
      migrations,
      type: 'postgres',
      host: this.getString('DB_HOST'),
      port: this.getNumber('DB_PORT'),
      username: this.getString('DB_USERNAME'),
      password: this.getString('DB_PASSWORD'),
      database: this.getString('DB_DATABASE'),
      migrationsRun: !this.isDevelopment,
      synchronize: this.isDevelopment,
      logging: this.getBoolean('ENABLE_ORM_LOGS'),
      namingStrategy: new SnakeNamingStrategy(),
    };
  }

  get authConfig() {
    return {
      privateKey: this.getString('JWT_PRIVATE_KEY'),
      publicKey: this.getString('JWT_PUBLIC_KEY'),
      jwtExpirationTime: this.getNumber('JWT_EXPIRATION_TIME'),
    };
  }

  get appConfig() {
    return {
      port: this.getString('PORT'),
    };
  }

  private get(key: string): string {
    const value = this.configService.get<string>(key);

    if (isNil(value)) {
      throw new Error(key + ' environment variable does not set'); // probably we should call process.exit() too to avoid locking the service
    }

    return value;
  }
}
