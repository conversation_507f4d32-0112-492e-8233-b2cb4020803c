import { ApiPropertyOptional } from '@nestjs/swagger';
import { Order } from '../constants';

export class SearchQuery {
  constructor() {
    this.name = undefined;
    this.q = undefined;
    this.maxDate = undefined;
    this.minDate = undefined;
  }

  readonly name?: string;

  readonly q?: string;

  readonly maxDate?: string;

  readonly minDate?: string;
}

export class PageOptionsDto {
  @ApiPropertyOptional({
    enum: Order,
    default: Order.ASC,
  })
  readonly order: Order = Order.ASC;

  @ApiPropertyOptional({
    minimum: 1,
    default: 1,
    type: Number,
  })
  readonly page: number = 1;

  @ApiPropertyOptional({
    minimum: 1,
    maximum: 50,
    default: 25,
    type: Number,
  })
  readonly take: number = 10;

  get skip(): number {
    return (this.page - 1) * this.take;
  }

  @ApiPropertyOptional({
    type: String,
  })
  readonly searchQuery?: string;

  @ApiPropertyOptional({
    type: String,
  })
  readonly orderBy?: string;
}
