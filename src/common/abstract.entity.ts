import {
  CreateDateColumn,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
} from 'typeorm';

import { type Constructor } from './types';
import { type AbstractDto } from './dto/abstract.dto';

/**
 * Abstract Entity
 */
export abstract class AbstractEntity<
  DTO extends AbstractDto = AbstractDto,
  O = never,
> {
  @PrimaryGeneratedColumn('uuid')
  id!: Uuid;

  @CreateDateColumn({
    type: 'timestamp',
  })
  createdAt!: Date;

  @UpdateDateColumn({
    type: 'timestamp',
  })
  updatedAt!: Date;

  toDto(options?: O): DTO {
    const dtoClass = Object.getPrototypeOf(this).dtoClass as Constructor<
      DTO,
      [AbstractEntity, O?]
    >;

    // eslint-disable-next-line @typescript-eslint/no-unnecessary-condition
    if (!dtoClass) {
      throw new Error(
        `You need to use @UseDto on class (${this.constructor.name}) be able to call toDto function`,
      );
    }

    // eslint-disable-next-line @typescript-eslint/no-unsafe-call, @typescript-eslint/no-unsafe-return
    return new dtoClass(this, options);
  }
}
