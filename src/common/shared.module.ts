import { Global, Module, type Provider } from '@nestjs/common';
import { CqrsModule } from '@nestjs/cqrs';

import { ApiConfigService } from '../common/services/api-config.service';
import { GeneratorService } from '../common/services/generator.service';

const providers: Provider[] = [ApiConfigService, GeneratorService];

@Global()
@Module({
  providers,
  imports: [CqrsModule],
  exports: [...providers, CqrsModule],
})
export class SharedModule {}
