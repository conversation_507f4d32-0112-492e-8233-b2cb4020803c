# Amazon Queue Parameter Fix Guide

## Current Issue

The error `MarketplaceIds must be an array` is occurring because there are still old jobs in the queue that were created before the parameter format fix.

### Error Details
```
[Nest] 15966  - 08/06/2025, 6:06:43 PM   ERROR [AmazonService] Error fetching orders: MarketplaceIds must be an array
[Nest] 15966  - 08/06/2025, 6:06:43 PM   ERROR [AmazonService] Error processing job order:
[Nest] 15966  - 08/06/2025, 6:06:43 PM   ERROR [AmazonService] Error: MarketplaceIds must be an array
```

## Root Cause

1. **Old Jobs in Queue**: Jobs created before the fix still have `MarketplaceIds` as a string
2. **Parameter Validation**: The new validation correctly catches the issue but rejects old jobs
3. **Queue Persistence**: Bull queue persists jobs in Redis, so old jobs remain

## Immediate Fix Steps

### Step 1: Clear the Queue
```bash
# Call the new endpoint to clear all jobs
POST /api/product/amazon/queue/clear
Authorization: Bearer <your-token>
```

**OR manually via Redis:**
```bash
# Connect to Redis
redis-cli

# Clear Amazon queue
DEL bull:Amazon:waiting
DEL bull:Amazon:active
DEL bull:Amazon:completed
DEL bull:Amazon:failed
DEL bull:Amazon:delayed
```

### Step 2: Restart the Service
```bash
# Restart the application to ensure clean state
docker restart <container-name>
```

### Step 3: Test the Fix
```bash
# Test with the optimized endpoint
POST /api/product/amazon/orders/last-month
Authorization: Bearer <your-token>
```

## Fixes Implemented

### 1. **Backward Compatibility Fix**
```typescript
private fixOrderParams(params: any): any {
  const fixedParams = { ...params };
  
  // Fix MarketplaceIds if it's a string
  if (typeof fixedParams.MarketplaceIds === 'string') {
    this.logger.warn('Converting MarketplaceIds from string to array');
    fixedParams.MarketplaceIds = [fixedParams.MarketplaceIds];
  }
  
  return fixedParams;
}
```

### 2. **Enhanced Queue Clearing**
```typescript
async clearQueue(): Promise<void> {
  try {
    const waiting = await this._Q.getWaiting();
    const active = await this._Q.getActive();
    const delayed = await this._Q.getDelayed();
    
    this.logger.log(`Clearing queue: ${waiting.length} waiting, ${active.length} active, ${delayed.length} delayed jobs`);
    
    await this._Q.clean(0, 'completed');
    await this._Q.clean(0, 'active');
    await this._Q.clean(0, 'failed');
    await this._Q.clean(0, 'delayed');
    await this._Q.empty();
    
    this.logger.log('Amazon queue completely cleared');
  } catch (error) {
    this.logger.error('Error clearing the queue:', error);
  }
}
```

### 3. **New API Endpoint**
```typescript
@Post('amazon/queue/clear')
@Auth([RoleType.ADMIN])
async clearAmazonQueue() {
  try {
    await this.amazonService.clearQueue();
    return { 
      success: true, 
      message: 'Amazon queue cleared successfully' 
    };
  } catch (error: any) {
    throw new HttpException(
      `Error clearing Amazon queue: ${error.message}`,
      500,
    );
  }
}
```

### 4. **Disabled Auto-Start**
Temporarily disabled automatic order job on startup to prevent conflicts:
```typescript
// Temporarily disable automatic order job to prevent conflicts
// await this.fetchLastMonthOrders();
// await this.orderJob();
```

## Verification Steps

### 1. Check Queue Status
```bash
# Monitor queue in Redis
redis-cli
> LLEN bull:Amazon:waiting
> LLEN bull:Amazon:active
> LLEN bull:Amazon:completed
```

### 2. Monitor Logs
```bash
# Watch for successful processing
docker logs <container> | grep "Amazon"
```

### 3. Test API Calls
```bash
# Should work without errors
POST /api/product/amazon/orders/last-month
```

## Expected Results After Fix

### ✅ **Success Indicators:**
- No more "MarketplaceIds must be an array" errors
- Successful order fetching
- Proper parameter format in logs
- Queue processing without errors

### 📊 **Log Examples:**
```
[Nest] LOG [AmazonService] Amazon queue completely cleared
[Nest] LOG [AmazonService] Fetching orders in 9 date chunks
[Nest] LOG [AmazonService] Processing chunk 1/9: 2025-06-06T07:00:00.000Z to 2025-06-14T06:59:59.999Z
[Nest] DEBUG [AmazonService] Request params: {"MarketplaceIds":["ATVPDKIKX0DER"],...}
```

## Prevention for Future

### 1. **Parameter Validation**
All new jobs now have proper validation and auto-fixing

### 2. **Queue Management**
Enhanced queue clearing removes all job types

### 3. **Backward Compatibility**
Auto-conversion of old parameter formats

### 4. **Manual Controls**
API endpoint to clear queue when needed

## Troubleshooting

### If Error Persists:
1. **Check Redis**: Ensure all Amazon queue keys are cleared
2. **Restart Service**: Full application restart
3. **Manual Queue Clear**: Use Redis CLI to manually clear
4. **Check Logs**: Look for parameter format in debug logs

### Redis Manual Cleanup:
```bash
redis-cli
> KEYS bull:Amazon:*
> DEL bull:Amazon:waiting bull:Amazon:active bull:Amazon:completed bull:Amazon:failed bull:Amazon:delayed
```

## Summary

The issue is caused by old jobs in the queue with incorrect parameter format. The fix includes:

1. ✅ **Backward compatibility** for old jobs
2. ✅ **Enhanced queue clearing** 
3. ✅ **Manual control endpoint**
4. ✅ **Proper parameter validation**

**Next Steps:**
1. Clear the queue using the endpoint or Redis
2. Restart the service
3. Test with the optimized order fetching endpoint

The system should then work correctly without the parameter format errors.
