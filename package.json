{"name": "knot-core", "version": "1.0.0", "description": "", "author": "", "private": true, "license": "MIT", "scripts": {"build": "nest build", "build:prod": "tsc -p tsconfig.build.json", "postbuild:prod": "copyfiles --up 1 src/**/*.json dist", "start:hmr": "node dist/main.hmr.ts", "start:dev": "ts-node src/main.ts", "start:prod": "node dist/main.js", "typeorm": "typeorm-ts-node-commonjs -d ./ormconfig.ts", "migration:generate": "npm run typeorm migration:generate", "migration:show": "npm run typeorm migration:show", "migration:run": "npm run typeorm migration:run", "migration:revert": "npm run typeorm migration:revert", "migration:create": "typeorm-ts-node-commonjs migration:create", "migrations:mark-complete": "ts-node src/mark-migrations-complete.ts", "new": "hygen new", "schema:drop": "yarn run typeorm schema:drop", "watch:dev": "ts-node-dev src/main.ts", "debug:dev": "cross-env TS_NODE_CACHE=false ts-node-dev --inspect --ignore '/^src/.*\\.spec\\.ts$/' src/main.ts", "webpack": "webpack --config webpack.config.js --progress", "lint": "eslint . --ext .ts", "lint:fix": "eslint --fix . --ext .ts", "test": "NODE_ENV=test jest", "test:watch": "NODE_ENV=test jest --watch", "test:cov": "NODE_ENV=test jest --coverage", "test:debug": "NODE_ENV=test node --inspect-brk -r ts-node/register node_modules/.bin/jest --runInBand", "test:e2e": "NODE_ENV=test jest --config ./test/jest-e2e.json", "docs:dev": "vuepress dev -p 7070", "docs:build": "DEPLOY_ENV=gh-pages vuepress build", "docs:deploy": "yarn docs:build && gh-pages -d .vuepress/dist", "prepare": "husky install", "release": "release-it"}, "dependencies": {"@aws-sdk/client-s3": "^3.729.0", "@heroicons/react": "^2.2.0", "@nestjs-modules/mailer": "^2.0.2", "@nestjs/bull": "^10.2.3", "@nestjs/cache-manager": "^2.3.0", "@nestjs/common": "^10.4.15", "@nestjs/config": "^3.3.0", "@nestjs/core": "^10.4.15", "@nestjs/cqrs": "^10.2.8", "@nestjs/jwt": "^10.2.0", "@nestjs/microservices": "^10.4.15", "@nestjs/passport": "^10.0.3", "@nestjs/platform-express": "^10.4.15", "@nestjs/schedule": "^4.1.2", "@nestjs/swagger": "^8.1.1", "@nestjs/terminus": "^10.2.3", "@nestjs/typeorm": "^10.0.2", "axios": "^1.7.9", "axios-auth-refresh": "^3.3.6", "bcrypt": "^5.1.1", "bull": "^4.16.5", "cache-manager": "^5.7.6", "cache-manager-redis-store": "^3.0.1", "class-transformer": "~0.5.1", "class-validator": "~0.14.1", "compression": "^1.7.5", "crypto": "^1.0.1", "date-fns": "^4.1.0", "eslint": "^9.18.0", "etsy-ts": "^3.12.0", "express": "^4.21.2", "express-ctx": "^0.1.1", "express-rate-limit": "^7.5.0", "google-auth-library": "^9.15.0", "google-spreadsheet": "^4.1.4", "googleapis": "^144.0.0", "helmet": "^8.0.0", "html-entities": "^2.5.2", "jsonwebtoken": "^9.0.2", "lodash": "^4.17.21", "mime-types": "^2.1.35", "moment-timezone": "^0.5.46", "morgan": "^1.10.0", "nestjs-i18n": "^10.5.0", "nodemailer": "^6.9.16", "passport": "^0.7.0", "passport-jwt": "^4.0.1", "pg": "^8.13.1", "redis": "^4.7.0", "reflect-metadata": "^0.2.2", "rxjs": "^7.8.1", "source-map-support": "^0.5.21", "swagger-ui-express": "^5.0.1", "tslib": "^2.8.1", "typeorm": "^0.3.20", "typeorm-transactional": "~0.5.0", "uuid": "^11.0.5", "xlsx": "^0.18.5"}, "devDependencies": {"@handfish/hygen": "^6.1.6", "@nestjs/cli": "^10.4.9", "@nestjs/testing": "^10.4.15", "@types/bcrypt": "^5.0.2", "@types/cache-manager-redis-store": "^2.0.4", "@types/compression": "^1.7.5", "@types/express": "^5.0.0", "@types/jest": "^29.5.14", "@types/jsonwebtoken": "^9.0.7", "@types/lodash": "^4.17.14", "@types/mime-types": "^2.1.4", "@types/morgan": "^1.9.9", "@types/multer": "^1.4.12", "@types/node": "^22.10.6", "@types/nodemailer": "^6.4.17", "@types/passport-jwt": "^4.0.1", "@types/supertest": "^6.0.2", "@types/uuid": "^10.0.0", "@types/webpack-env": "^1.18.5", "@vuepress/client": "2.0.0-beta.66", "clean-webpack-plugin": "^4.0.0", "copyfiles": "^2.4.1", "cross-env": "^7.0.3", "gh-pages": "^6.3.0", "husky": "^9.1.7", "jest": "^29.7.0", "lint-staged": "~15.3.0", "prettier": "^3.4.2", "release-it": "^18.1.1", "supertest": "^7.0.0", "ts-jest": "^29.2.5", "ts-node": "^10.9.2", "ts-node-dev": "^2.0.0", "typescript": "^5.7.3", "vue": "^3.5.13", "webpack": "^5.97.1", "webpack-cli": "^6.0.1", "webpack-node-externals": "^3.0.0"}, "jest": {"moduleFileExtensions": ["js", "json", "ts"], "rootDir": "src", "testRegex": ".spec.ts$", "transform": {"^.+\\.(t|j)s$": "ts-jest"}, "coverageDirectory": "../coverage", "testEnvironment": "node"}}