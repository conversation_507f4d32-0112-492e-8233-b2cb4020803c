# Amazon API Rate Limiting Improvements

## Overview
This document outlines the comprehensive improvements made to the Amazon service to prevent rate limiting issues and ensure compliance with Amazon SP-API rate limits.

## Key Issues Identified

### 1. Critical Rate Limits
- **Orders API (`getOrders`)**: 0.0167 req/sec (1 request per 60 seconds)
- **Orders API (`getOrderItems`)**: 0.5 req/sec (1 request per 2 seconds)
- **Feeds API (`createFeed`)**: 0.0083 req/sec (1 request per 120 seconds)
- **FBA Inventory API**: ~2 req/sec (1 request per 0.5 seconds)

### 2. Previous Implementation Issues
- Insufficient delays between API calls
- No exponential backoff for 429 errors
- Aggressive queue processing
- Multiple concurrent requests without throttling
- Fixed 5-second delay for all 429 errors

## Improvements Implemented

### 1. Enhanced Error Handling with Exponential Backoff
```typescript
// Exponential backoff: 2^retryCount * 5000ms + jitter
const baseDelay = Math.pow(2, config.retryCount) * 5000;
const jitter = Math.random() * 2000; // Add 0-2s jitter
```

**Benefits:**
- Prevents overwhelming the API during rate limiting
- Adds randomization to avoid thundering herd problems
- Increases retry delays progressively
- Handles server errors (5xx) with appropriate retries

### 2. Rate Limiter Class
```typescript
class AmazonRateLimiter {
  private lastCallTimes: Map<string, number> = new Map();
  
  async waitForRateLimit(endpoint: string): Promise<void> {
    // Calculates appropriate delays based on endpoint
  }
}
```

**Features:**
- Tracks last call times per endpoint
- Enforces minimum intervals based on API rate limits
- Integrated into Axios request interceptor

### 3. Queue Configuration Improvements
```typescript
limiter: {
  max: 1, // Process only 1 job at a time
  duration: 5000, // Per 5 seconds
}
```

**Changes:**
- Reduced concurrent job processing from 3 to 1
- Increased retry attempts from 3 to 5
- Added exponential backoff with 10-second base delay
- Improved job cleanup settings

### 4. API-Specific Delays

#### Orders API
- **getOrders**: 65-second delay between requests
- **getOrderItems**: 3-second delay between requests
- Staggered order item processing with 5-second intervals

#### Feeds API
- **createFeed**: 125-second delay between requests
- Reduced batch size from 200 to 50 products
- 130-second delay between batch processing

#### Inventory API
- 1-second delay between requests
- Batch processing with 100ms delays every 10 items

### 5. Enhanced Monitoring and Logging
```typescript
private logRateLimitHeaders(response: any): void {
  const rateLimitHeader = response.headers?.['x-amzn-ratelimit-limit'];
  if (rateLimitHeader) {
    this.logger.debug(`Rate limit info: ${rateLimitHeader}`);
  }
}
```

**Features:**
- Logs rate limit headers from Amazon responses
- Detailed error logging with retry counts
- API health monitoring every 5 minutes
- Request/response timing information

### 6. Improved Job Processing
```typescript
private getJobDelay(type: string): number {
  switch (type) {
    case 'order': return 65000; // 65 seconds
    case 'itemDetail': return 3000; // 3 seconds
    case 'inventory': return 1000; // 1 second
    case 'feed': return 125000; // 125 seconds
    default: return 5000; // Default 5 seconds
  }
}
```

**Benefits:**
- Job-specific delays based on API rate limits
- Proper error handling with retry mechanisms
- Reduced queue congestion

## Configuration Changes

### Bull Queue Settings
- **Concurrency**: Reduced to 1 job at a time
- **Retry Attempts**: Increased to 5
- **Backoff Strategy**: Exponential with 10-second base
- **Cleanup**: Better job removal policies

### Axios Configuration
- **Timeout**: Increased to 60 seconds
- **Request Interceptor**: Added rate limiting
- **Response Interceptor**: Enhanced error handling and monitoring

## Best Practices Implemented

1. **Respect API Rate Limits**: All delays exceed minimum requirements
2. **Exponential Backoff**: Progressive retry delays with jitter
3. **Error Recovery**: Comprehensive error handling for all scenarios
4. **Monitoring**: Real-time rate limit header tracking
5. **Queue Management**: Conservative job processing approach
6. **Batch Processing**: Smaller batches with appropriate delays

## Expected Results

1. **Elimination of 429 Rate Limiting Errors**
2. **Improved API Reliability**
3. **Better Error Recovery**
4. **Reduced System Load**
5. **Compliance with Amazon SP-API Guidelines**

## Monitoring Recommendations

1. Monitor logs for rate limiting warnings
2. Track API response times and error rates
3. Adjust delays if new rate limits are encountered
4. Review queue processing metrics regularly

## Future Enhancements

1. Dynamic rate limit adjustment based on response headers
2. Circuit breaker pattern for API failures
3. Metrics collection for performance analysis
4. Automated alerting for rate limiting issues
